#pragma once
#include "Globals.h"
#include "ImGui/imgui.h"

// Constantes de cores para o sistema de captura de teclas
namespace KeyCaptureColors {
    // Cores principais do tema laranja/dourado
    const ImVec4 BACKGROUND_COLOR = ImVec4(0.10f, 0.10f, 0.12f, 0.95f); // Fundo escuro
    const ImVec4 TEXT_COLOR = ImVec4(1.0f, 0.84f, 0.0f, 1.0f);          // Amar<PERSON> dourado
    const ImVec4 ACCENT_COLOR = ImVec4(1.0f, 0.84f, 0.0f, 1.0f);        // Amarelo dourado
    const ImVec4 HEADER_COLOR = ImVec4(0.45f, 0.25f, 0.08f, 0.9f);      // Laranja escuro
    const ImVec4 ACTIVE_TAB_COLOR = ImVec4(0.70f, 0.40f, 0.10f, 1.00f); // Laranja
}

//---------------------------------------------------------------------
// 🔑	Sistema de Captura de Teclas
//---------------------------------------------------------------------
// Este sistema permite capturar qualquer tecla do teclado, mouse ou gamepad
// para ser usada como atalho para ativar funções no programa.
//---------------------------------------------------------------------

namespace KeyCaptureSystem {
    // Tipos de teclas que podem ser capturadas
    enum class KeyType {
        Keyboard,   // Tecla do teclado
        Mouse,      // Botão do mouse
        Gamepad     // Botão do gamepad
    };

    // Tipos de captura de tecla
    enum class KeyCaptureType {
        None,                   // Nenhuma captura em andamento
        AimbotKey,              // Tecla para ativar o aimbot (teclado/mouse)
        BulletTPKey,            // Tecla para ativar o bullet TP (teclado/mouse)
        GamepadAimbotButton,    // Botão para ativar o aimbot (gamepad)
        GamepadBulletTPButton,  // Botão para ativar o bullet TP (gamepad)
        TeamTargetKey,          // Tecla para alternar o modo de alvo de equipe (teclado/mouse)
        GamepadTeamTargetButton,// Botão para alternar o modo de alvo de equipe (gamepad)
        Custom                  // Tipo personalizado para extensões futuras
    };

    // Estrutura para armazenar informações sobre uma tecla capturada
    struct CapturedKey {
        KeyType type;       // Tipo de tecla (teclado, mouse, gamepad)
        int keyCode;        // Código da tecla
        std::string name;   // Nome da tecla
    };

    // Variáveis globais
    inline bool IsCapturing = false;
    inline KeyCaptureType CurrentCaptureType = KeyCaptureType::None;
    inline CapturedKey LastCapturedKey = { KeyType::Keyboard, 0, "None" };
    inline bool KeysInitialized = false;
    inline int KeyErrorCount = 0;
    inline std::chrono::steady_clock::time_point LastKeyPressTime = std::chrono::steady_clock::now();
    inline std::chrono::steady_clock::time_point LastHeartbeatTime = std::chrono::steady_clock::now();
    inline int HeartbeatCount = 0;
    inline bool KeysNeedReset = false;
    inline int KeyResetAttempts = 0;
    inline const int MAX_RESET_ATTEMPTS = 3;
    inline const int HEARTBEAT_INTERVAL_SECONDS = 60; // Verificar a cada 1 minuto

    // Declaração antecipada da função ApplyCapturedKey
    inline void ApplyCapturedKey();

    // Função para limpar o cache de teclas e forçar a reinicialização
    inline void ClearKeyCache() {
        printf("Clearing key cache and forcing reinitialization\n");
        Keys::KeyCache::FKeyCache.clear();
        KeysInitialized = false;
        KeysNeedReset = true;
        KeyResetAttempts = 0;
    }

    // Função para reinicializar o estado das teclas
    inline void ResetKeyState() {
        printf("Resetting key state (attempt %d)\n", ++KeyResetAttempts);
        try {
            // Verificar se os índices do botão do gamepad são válidos
            if (mods::gamepad_aimbot_button < 0 || mods::gamepad_aimbot_button >= 14) {
                printf("Invalid gamepad aimbot button index: %d, resetting to 0\n", mods::gamepad_aimbot_button);
                mods::gamepad_aimbot_button = 0;
            }

            if (mods::gamepad_bullet_tp_button < 0 || mods::gamepad_bullet_tp_button >= 14) {
                printf("Invalid gamepad bullet TP button index: %d, resetting to 0\n", mods::gamepad_bullet_tp_button);
                mods::gamepad_bullet_tp_button = 0;
            }

            if (mods::gamepadTeamTargetButton < 0 || mods::gamepadTeamTargetButton >= 14) {
                printf("Invalid gamepad team target button index: %d, resetting to 0\n", mods::gamepadTeamTargetButton);
                mods::gamepadTeamTargetButton = 0;
            }

            // Obter as teclas com tratamento de erros
            SDK::FKey* aimbotGamepadButton = Keys::GetGamepadButtonByIndex(mods::gamepad_aimbot_button);
            SDK::FKey* bulletTPGamepadButton = Keys::GetGamepadButtonByIndex(mods::gamepad_bullet_tp_button);
            SDK::FKey* teamTargetGamepadButton = Keys::GetGamepadButtonByIndex(mods::gamepadTeamTargetButton);

            // Verificar se os ponteiros são válidos usando safe_memory_check
            if (!IsValidSDKPtr(aimbotGamepadButton)) {
                printf("Invalid aimbot gamepad button pointer, using default\n");
                aimbotGamepadButton = Keys::GetGamepadButtonByIndex(0);
            }

            if (!IsValidSDKPtr(bulletTPGamepadButton)) {
                printf("Invalid bullet TP gamepad button pointer, using default\n");
                bulletTPGamepadButton = Keys::GetGamepadButtonByIndex(0);
            }

            if (!IsValidSDKPtr(teamTargetGamepadButton)) {
                printf("Invalid team target gamepad button pointer, using default\n");
                teamTargetGamepadButton = Keys::GetGamepadButtonByIndex(0);
            }

            // Reinicializar as teclas configuráveis
            Keys::CurrentAimbotKey = Keys::GetFKeyFromKeyCode(mods::aimbot_key);
            Keys::CurrentBulletTPKey = Keys::GetFKeyFromKeyCode(mods::bullet_tp_key);
            Keys::CurrentTeamTargetKey = Keys::GetFKeyFromKeyCode(mods::teamTargetKey);

            // Reinicializar o estado de captura
            IsCapturing = false;
            CurrentCaptureType = KeyCaptureType::None;

            // Resetar contador de erros
            KeyErrorCount = 0;

            // Atualizar os tempos
            LastKeyPressTime = std::chrono::steady_clock::now();
            LastHeartbeatTime = std::chrono::steady_clock::now();
            HeartbeatCount = 0;

            // Marcar as teclas como inicializadas
            KeysInitialized = true;
            KeysNeedReset = false;
            KeyResetAttempts = 0;

            printf("Key state reset successfully\n");
        } catch (const std::exception& e) {
            printf("Exception in ResetKeyState: %s\n", e.what());

            // Se excedeu o número máximo de tentativas, limpar o cache
            if (KeyResetAttempts >= MAX_RESET_ATTEMPTS) {
                ClearKeyCache();
            }
        } catch (...) {
            printf("Unknown exception in ResetKeyState\n");

            // Se excedeu o número máximo de tentativas, limpar o cache
            if (KeyResetAttempts >= MAX_RESET_ATTEMPTS) {
                ClearKeyCache();
            }
        }
    }

    // Função para iniciar a captura de tecla
    inline void StartCapture(KeyCaptureType captureType) {
        IsCapturing = true;
        CurrentCaptureType = captureType;
        // Log de início de captura
        printf("Key capture started for type: %d\n", static_cast<int>(captureType));
    }

    // Função para finalizar a captura de tecla
    inline void EndCapture() {
        IsCapturing = false;
        CurrentCaptureType = KeyCaptureType::None;
        // Log de fim de captura
        printf("Key capture ended\n");
    }

    // Função para verificar a saúde do sistema de teclas (heartbeat)
    inline void CheckKeySystemHealth() {
        auto now = std::chrono::steady_clock::now();

        // Verificar se as teclas precisam ser reinicializadas
        if (KeysNeedReset) {
            ResetKeyState();
            return;
        }

        // Verificar se as teclas foram inicializadas
        if (!KeysInitialized) {
            printf("Keys not initialized, initializing now\n");
            ResetKeyState();
            return;
        }

        // Verificar se passou muito tempo desde o último heartbeat
        auto heartbeatElapsed = std::chrono::duration_cast<std::chrono::seconds>(now - LastHeartbeatTime).count();
        if (heartbeatElapsed >= HEARTBEAT_INTERVAL_SECONDS) {
            HeartbeatCount++;
            printf("Key system heartbeat #%d\n", HeartbeatCount);

            // Verificar se os ponteiros das teclas ainda são válidos
            bool keysValid = true;

            // Verificar se o PlayerController é válido
            SDK::APlayerController* PlayerController = Variables::PlayerController;
            if (!IsValidObjectPtr(PlayerController)) {
                printf("Invalid PlayerController in heartbeat check\n");
                keysValid = false;
            }

            // Verificar se as teclas do gamepad são válidas
            SDK::FKey* aimbotGamepadButton = Keys::GetGamepadButtonByIndex(mods::gamepad_aimbot_button);
            SDK::FKey* bulletTPGamepadButton = Keys::GetGamepadButtonByIndex(mods::gamepad_bullet_tp_button);

            if (!IsValidSDKPtr(aimbotGamepadButton) || !IsValidSDKPtr(bulletTPGamepadButton)) {
                printf("Invalid gamepad button pointers in heartbeat check\n");
                keysValid = false;
            }

            // Se alguma verificação falhou, reinicializar as teclas
            if (!keysValid) {
                printf("Key validation failed in heartbeat check, resetting keys\n");
                ResetKeyState();
            }

            // Atualizar o tempo do último heartbeat
            LastHeartbeatTime = now;
        }

        // Verificar se passou muito tempo desde a última tecla pressionada
        auto keyPressElapsed = std::chrono::duration_cast<std::chrono::seconds>(now - LastKeyPressTime).count();

        // Remover o timeout automático - as teclas das funcionalidades são verificadas diretamente
        // nos módulos, então não precisamos resetar o sistema por falta de atividade de captura
        // if (keyPressElapsed > 300) {
        //     printf("No key press detected for %lld seconds, resetting key state\n", keyPressElapsed);
        //     ResetKeyState();
        // }
    }

    //---------------------------------------------------------------------
    // 🔍	Verificação de Teclas para Captura (Configuração)
    //---------------------------------------------------------------------
    inline void CheckForKeyCapture() {
        // Esta função só deve ser chamada quando estamos capturando teclas para configuração
        if (!IsCapturing) {
            return;
        }

        try {
            ImGuiIO& io = ImGui::GetIO();

            // Verificar teclas do teclado
            for (int i = 0; i < IM_ARRAYSIZE(io.KeysDown); i++) {
                if (io.KeysDown[i] && io.KeysDownDuration[i] == 0.0f) {
                    // Tecla do teclado pressionada
                    LastCapturedKey.type = KeyType::Keyboard;
                    LastCapturedKey.keyCode = i;

                    // Tentar encontrar o nome da tecla no mapa
                    bool foundName = false;
                    for (const auto& pair : Keys::KeyNames) {
                        if (pair.first == i) {
                            LastCapturedKey.name = pair.second;
                            foundName = true;
                            break;
                        }
                    }

                    if (!foundName) {
                        LastCapturedKey.name = "Key_" + std::to_string(i);
                    }

                    // Atualizar o tempo da última tecla pressionada
                    LastKeyPressTime = std::chrono::steady_clock::now();

                    // Aplicar a tecla capturada com base no tipo de captura
                    ApplyCapturedKey();
                    EndCapture();
                    return;
                }
            }

            // Verificar botões do mouse
            for (int i = 0; i < 5; i++) {
                if (ImGui::IsMouseClicked(i)) {
                    // Botão do mouse pressionado
                    LastCapturedKey.type = KeyType::Mouse;
                    LastCapturedKey.keyCode = i + 1; // Ajustar para o código correto (1-5)

                    switch (i) {
                        case 0: LastCapturedKey.name = "LeftMouseButton"; break;
                        case 1: LastCapturedKey.name = "RightMouseButton"; break;
                        case 2: LastCapturedKey.name = "MiddleMouseButton"; break;
                        case 3: LastCapturedKey.name = "ThumbMouseButton"; break;
                        case 4: LastCapturedKey.name = "ThumbMouseButton2"; break;
                        default: LastCapturedKey.name = "Mouse_" + std::to_string(i); break;
                    }

                    // Atualizar o tempo da última tecla pressionada
                    LastKeyPressTime = std::chrono::steady_clock::now();

                    // Aplicar a tecla capturada com base no tipo de captura
                    ApplyCapturedKey();
                    EndCapture();
                    return;
                }
            }

            // Verificar botões do gamepad
            // Obter o PlayerController para verificar os botões do gamepad
            SDK::APlayerController* PlayerController = Variables::PlayerController;
            if (IsValidObjectPtr(PlayerController)) {
                // Verificar cada botão do gamepad
                for (int i = 0; i < 14; i++) {
                    SDK::FKey* gamepadButton = Keys::GetGamepadButtonByIndex(i);
                    if (gamepadButton && PlayerController->IsInputKeyDown(*gamepadButton)) {
                        // Botão do gamepad pressionado
                        LastCapturedKey.type = KeyType::Gamepad;
                        LastCapturedKey.keyCode = i;
                        LastCapturedKey.name = Keys::GamepadButtonNames[i];

                        // Atualizar o tempo da última tecla pressionada
                        LastKeyPressTime = std::chrono::steady_clock::now();

                        // Aplicar a tecla capturada com base no tipo de captura
                        ApplyCapturedKey();
                        EndCapture();
                        return;
                    }
                }
            } else {
                // Se o PlayerController não for válido, incrementar o contador de erros
                KeyErrorCount++;
                printf("Invalid PlayerController in CheckForKeyPress, error count: %d\n", KeyErrorCount);

                // Aumentar a tolerância a erros - só resetar após 20 erros consecutivos
                if (KeyErrorCount > 20) {
                    ResetKeyState();
                }
            }
        } catch (const std::exception& e) {
            // Se ocorrer uma exceção, incrementar o contador de erros
            KeyErrorCount++;
            printf("Exception in CheckForKeyCapture: %s, error count: %d\n", e.what(), KeyErrorCount);

            // Aumentar a tolerância a erros - só resetar após 20 erros consecutivos
            if (KeyErrorCount > 20) {
                ResetKeyState();
            }
        } catch (...) {
            // Se ocorrer uma exceção desconhecida, incrementar o contador de erros
            KeyErrorCount++;
            printf("Unknown exception in CheckForKeyCapture, error count: %d\n", KeyErrorCount);

            // Aumentar a tolerância a erros - só resetar após 20 erros consecutivos
            if (KeyErrorCount > 20) {
                ResetKeyState();
            }
        }
    }

    //---------------------------------------------------------------------
    // 🔄	Reset do Contador de Erros
    //---------------------------------------------------------------------
    inline void ResetErrorCount() {
        if (KeyErrorCount > 0) {
            printf("Resetting error count from %d to 0\n", KeyErrorCount);
            KeyErrorCount = 0;
        }
    }

    //---------------------------------------------------------------------
    // 🎮	Verificação de Teclas das Funcionalidades (Loop Principal)
    //---------------------------------------------------------------------
    inline void CheckForKeyPress() {
        // Sempre verificar a saúde do sistema de teclas
        CheckKeySystemHealth();

        // Se estamos capturando teclas, usar a função específica para captura
        if (IsCapturing) {
            CheckForKeyCapture();
        }

        // Não fazer mais nada aqui - a verificação das teclas das funcionalidades
        // é feita diretamente nos módulos (CheatModules.h) usando PlayerController->IsInputKeyDown()
    }

    // Função para aplicar a tecla capturada com base no tipo de captura
    inline void ApplyCapturedKey() {
        try {
            // Verificar se a tecla capturada é válida
            if (LastCapturedKey.keyCode < 0) {
                printf("Invalid key code: %d, ignoring capture\n", LastCapturedKey.keyCode);
                return;
            }

            // Limpar o cache de teclas antes de aplicar a nova tecla
            // Isso garante que a nova tecla será criada corretamente
            if (LastCapturedKey.type == KeyType::Keyboard || LastCapturedKey.type == KeyType::Mouse) {
                // Remover apenas a tecla específica do cache, se existir
                if (Keys::KeyCache::FKeyCache.count(LastCapturedKey.keyCode)) {
                    printf("Removing key %d from cache before applying\n", LastCapturedKey.keyCode);
                    Keys::KeyCache::FKeyCache.erase(LastCapturedKey.keyCode);
                }
            }

            switch (CurrentCaptureType) {
                case KeyCaptureType::AimbotKey:
                    if (LastCapturedKey.type == KeyType::Keyboard || LastCapturedKey.type == KeyType::Mouse) {
                        mods::aimbot_key = LastCapturedKey.keyCode;
                        printf("Aimbot key set to: %s (code: %d)\n", LastCapturedKey.name.c_str(), LastCapturedKey.keyCode);
                    }
                    break;

                case KeyCaptureType::BulletTPKey:
                    if (LastCapturedKey.type == KeyType::Keyboard || LastCapturedKey.type == KeyType::Mouse) {
                        mods::bullet_tp_key = LastCapturedKey.keyCode;
                        printf("Bullet TP key set to: %s (code: %d)\n", LastCapturedKey.name.c_str(), LastCapturedKey.keyCode);
                    }
                    break;

                case KeyCaptureType::GamepadAimbotButton:
                    if (LastCapturedKey.type == KeyType::Gamepad) {
                        if (LastCapturedKey.keyCode >= 0 && LastCapturedKey.keyCode < 14) {
                            mods::gamepad_aimbot_button = LastCapturedKey.keyCode;
                            printf("Gamepad Aimbot button set to: %s (code: %d)\n", LastCapturedKey.name.c_str(), LastCapturedKey.keyCode);
                        } else {
                            printf("Invalid gamepad button index: %d, ignoring\n", LastCapturedKey.keyCode);
                        }
                    }
                    break;

                case KeyCaptureType::GamepadBulletTPButton:
                    if (LastCapturedKey.type == KeyType::Gamepad) {
                        if (LastCapturedKey.keyCode >= 0 && LastCapturedKey.keyCode < 14) {
                            mods::gamepad_bullet_tp_button = LastCapturedKey.keyCode;
                            printf("Gamepad Bullet TP button set to: %s (code: %d)\n", LastCapturedKey.name.c_str(), LastCapturedKey.keyCode);
                        } else {
                            printf("Invalid gamepad button index: %d, ignoring\n", LastCapturedKey.keyCode);
                        }
                    }
                    break;

                case KeyCaptureType::TeamTargetKey:
                    if (LastCapturedKey.type == KeyType::Keyboard || LastCapturedKey.type == KeyType::Mouse) {
                        mods::teamTargetKey = LastCapturedKey.keyCode;
                        printf("Team Target key set to: %s (code: %d)\n", LastCapturedKey.name.c_str(), LastCapturedKey.keyCode);
                    }
                    break;

                case KeyCaptureType::GamepadTeamTargetButton:
                    if (LastCapturedKey.type == KeyType::Gamepad) {
                        if (LastCapturedKey.keyCode >= 0 && LastCapturedKey.keyCode < 14) {
                            mods::gamepadTeamTargetButton = LastCapturedKey.keyCode;
                            printf("Gamepad Team Target button set to: %s (code: %d)\n", LastCapturedKey.name.c_str(), LastCapturedKey.keyCode);
                        } else {
                            printf("Invalid gamepad button index: %d, ignoring\n", LastCapturedKey.keyCode);
                        }
                    }
                    break;

                case KeyCaptureType::Custom:
                    // Para implementações futuras
                    printf("Custom key captured: %s (code: %d)\n", LastCapturedKey.name.c_str(), LastCapturedKey.keyCode);
                    break;

                default:
                    printf("Unknown capture type: %d\n", static_cast<int>(CurrentCaptureType));
                    break;
            }

            // Atualizar as teclas configuráveis com verificação de erros
            try {
                // Verificar se o índice do botão do gamepad é válido
                if (mods::gamepad_aimbot_button < 0 || mods::gamepad_aimbot_button >= 14) {
                    printf("Invalid gamepad aimbot button index: %d, resetting to 0\n", mods::gamepad_aimbot_button);
                    mods::gamepad_aimbot_button = 0;
                }

                if (mods::gamepad_bullet_tp_button < 0 || mods::gamepad_bullet_tp_button >= 14) {
                    printf("Invalid gamepad bullet TP button index: %d, resetting to 0\n", mods::gamepad_bullet_tp_button);
                    mods::gamepad_bullet_tp_button = 0;
                }

                if (mods::gamepadTeamTargetButton < 0 || mods::gamepadTeamTargetButton >= 14) {
                    printf("Invalid gamepad team target button index: %d, resetting to 0\n", mods::gamepadTeamTargetButton);
                    mods::gamepadTeamTargetButton = 0;
                }

                // Obter as teclas com tratamento de erros
                SDK::FKey* aimbotGamepadButton = Keys::GetGamepadButtonByIndex(mods::gamepad_aimbot_button);
                SDK::FKey* bulletTPGamepadButton = Keys::GetGamepadButtonByIndex(mods::gamepad_bullet_tp_button);
                SDK::FKey* teamTargetGamepadButton = Keys::GetGamepadButtonByIndex(mods::gamepadTeamTargetButton);

                // Verificar se os ponteiros são válidos usando safe_memory_check
                if (!IsValidSDKPtr(aimbotGamepadButton)) {
                    printf("Invalid aimbot gamepad button pointer, using default\n");
                    aimbotGamepadButton = Keys::GetGamepadButtonByIndex(0);
                }

                if (!IsValidSDKPtr(bulletTPGamepadButton)) {
                    printf("Invalid bullet TP gamepad button pointer, using default\n");
                    bulletTPGamepadButton = Keys::GetGamepadButtonByIndex(0);
                }

                if (!IsValidSDKPtr(teamTargetGamepadButton)) {
                    printf("Invalid team target gamepad button pointer, using default\n");
                    teamTargetGamepadButton = Keys::GetGamepadButtonByIndex(0);
                }

                // Atualizar as teclas configuráveis
                Keys::CurrentAimbotKey = Keys::GetFKeyFromKeyCode(mods::aimbot_key);
                Keys::CurrentBulletTPKey = Keys::GetFKeyFromKeyCode(mods::bullet_tp_key);
                Keys::CurrentTeamTargetKey = Keys::GetFKeyFromKeyCode(mods::teamTargetKey);

                // Marcar as teclas como inicializadas
                KeysInitialized = true;

                // Resetar contador de erros
                KeyErrorCount = 0;

                // Atualizar o tempo da última tecla pressionada
                LastKeyPressTime = std::chrono::steady_clock::now();

                printf("Keys updated successfully\n");
            } catch (const std::exception& e) {
                // Se ocorrer uma exceção, incrementar o contador de erros
                KeyErrorCount++;
                printf("Exception in ApplyCapturedKey when updating keys: %s, error count: %d\n", e.what(), KeyErrorCount);

                // Aumentar a tolerância a erros - só resetar após 20 erros consecutivos
                if (KeyErrorCount > 20) {
                    ResetKeyState();
                }
            } catch (...) {
                // Se ocorrer uma exceção desconhecida, incrementar o contador de erros
                KeyErrorCount++;
                printf("Unknown exception in ApplyCapturedKey when updating keys, error count: %d\n", KeyErrorCount);

                // Aumentar a tolerância a erros - só resetar após 20 erros consecutivos
                if (KeyErrorCount > 20) {
                    ResetKeyState();
                }
            }
        } catch (const std::exception& e) {
            // Se ocorrer uma exceção, incrementar o contador de erros
            KeyErrorCount++;
            printf("Exception in ApplyCapturedKey: %s, error count: %d\n", e.what(), KeyErrorCount);

            // Aumentar a tolerância a erros - só resetar após 20 erros consecutivos
            if (KeyErrorCount > 20) {
                ResetKeyState();
            }
        } catch (...) {
            // Se ocorrer uma exceção desconhecida, incrementar o contador de erros
            KeyErrorCount++;
            printf("Unknown exception in ApplyCapturedKey, error count: %d\n", KeyErrorCount);

            // Aumentar a tolerância a erros - só resetar após 20 erros consecutivos
            if (KeyErrorCount > 20) {
                ResetKeyState();
            }
        }
    }
    // Função para desenhar o status da captura de tecla
    inline void DrawCaptureStatus() {
        if (IsCapturing) {
            // Usar as cores definidas localmente
            ImGui::TextColored(KeyCaptureColors::ACCENT_COLOR, "Pressione qualquer tecla...");
        }
    }

    // Função para limpar um atalho com base no tipo de captura
    inline void ClearShortcut(KeyCaptureType captureType) {
        try {
            // Limpar o cache de teclas relacionado ao atalho
            int keyCodeToRemove = -1;

            switch (captureType) {
                case KeyCaptureType::AimbotKey:
                    keyCodeToRemove = mods::aimbot_key;
                    mods::aimbot_key = 0; // 0 representa "None"
                    printf("Aimbot key cleared\n");
                    break;

                case KeyCaptureType::BulletTPKey:
                    keyCodeToRemove = mods::bullet_tp_key;
                    mods::bullet_tp_key = 0;
                    printf("Bullet TP key cleared\n");
                    break;

                case KeyCaptureType::GamepadAimbotButton:
                    mods::gamepad_aimbot_button = 0; // Usar 0 em vez de -1 para evitar problemas
                    printf("Gamepad Aimbot button cleared\n");
                    break;

                case KeyCaptureType::GamepadBulletTPButton:
                    mods::gamepad_bullet_tp_button = 0; // Usar 0 em vez de -1 para evitar problemas
                    printf("Gamepad Bullet TP button cleared\n");
                    break;

                case KeyCaptureType::TeamTargetKey:
                    keyCodeToRemove = mods::teamTargetKey;
                    mods::teamTargetKey = 0;
                    printf("Team Target key cleared\n");
                    break;

                case KeyCaptureType::GamepadTeamTargetButton:
                    mods::gamepadTeamTargetButton = 0; // Usar 0 em vez de -1 para evitar problemas
                    printf("Gamepad Team Target button cleared\n");
                    break;

                default:
                    printf("Unknown capture type: %d\n", static_cast<int>(captureType));
                    break;
            }

            // Remover a tecla do cache, se necessário
            if (keyCodeToRemove > 0 && Keys::KeyCache::FKeyCache.count(keyCodeToRemove)) {
                printf("Removing key %d from cache after clearing shortcut\n", keyCodeToRemove);
                Keys::KeyCache::FKeyCache.erase(keyCodeToRemove);
            }

            // Atualizar as teclas configuráveis com tratamento de erros
            try {
                // Obter as teclas com tratamento de erros
                SDK::FKey* aimbotGamepadButton = Keys::GetGamepadButtonByIndex(mods::gamepad_aimbot_button);
                SDK::FKey* bulletTPGamepadButton = Keys::GetGamepadButtonByIndex(mods::gamepad_bullet_tp_button);
                SDK::FKey* teamTargetGamepadButton = Keys::GetGamepadButtonByIndex(mods::gamepadTeamTargetButton);

                // Verificar se os ponteiros são válidos usando safe_memory_check
                if (!IsValidSDKPtr(aimbotGamepadButton)) {
                    printf("Invalid aimbot gamepad button pointer, using default\n");
                    aimbotGamepadButton = Keys::GetGamepadButtonByIndex(0);
                }

                if (!IsValidSDKPtr(bulletTPGamepadButton)) {
                    printf("Invalid bullet TP gamepad button pointer, using default\n");
                    bulletTPGamepadButton = Keys::GetGamepadButtonByIndex(0);
                }

                if (!IsValidSDKPtr(teamTargetGamepadButton)) {
                    printf("Invalid team target gamepad button pointer, using default\n");
                    teamTargetGamepadButton = Keys::GetGamepadButtonByIndex(0);
                }

                // Atualizar as teclas configuráveis
                Keys::CurrentAimbotKey = Keys::GetFKeyFromKeyCode(mods::aimbot_key);
                Keys::CurrentBulletTPKey = Keys::GetFKeyFromKeyCode(mods::bullet_tp_key);
                Keys::CurrentTeamTargetKey = Keys::GetFKeyFromKeyCode(mods::teamTargetKey);

                // Atualizar o tempo da última tecla pressionada
                LastKeyPressTime = std::chrono::steady_clock::now();

                // Resetar contador de erros
                KeyErrorCount = 0;

                printf("Keys updated successfully after clearing\n");
            } catch (const std::exception& e) {
                // Se ocorrer uma exceção, incrementar o contador de erros
                KeyErrorCount++;
                printf("Exception in ClearShortcut when updating keys: %s, error count: %d\n", e.what(), KeyErrorCount);

                // Aumentar a tolerância a erros - só resetar após 20 erros consecutivos
                if (KeyErrorCount > 20) {
                    ResetKeyState();
                }
            } catch (...) {
                // Se ocorrer uma exceção desconhecida, incrementar o contador de erros
                KeyErrorCount++;
                printf("Unknown exception in ClearShortcut when updating keys, error count: %d\n", KeyErrorCount);

                // Aumentar a tolerância a erros - só resetar após 20 erros consecutivos
                if (KeyErrorCount > 20) {
                    ResetKeyState();
                }
            }
        } catch (const std::exception& e) {
            // Se ocorrer uma exceção, incrementar o contador de erros
            KeyErrorCount++;
            printf("Exception in ClearShortcut: %s, error count: %d\n", e.what(), KeyErrorCount);

            // Aumentar a tolerância a erros - só resetar após 20 erros consecutivos
            if (KeyErrorCount > 20) {
                ResetKeyState();
            }
        } catch (...) {
            // Se ocorrer uma exceção desconhecida, incrementar o contador de erros
            KeyErrorCount++;
            printf("Unknown exception in ClearShortcut, error count: %d\n", KeyErrorCount);

            // Aumentar a tolerância a erros - só resetar após 20 erros consecutivos
            if (KeyErrorCount > 20) {
                ResetKeyState();
            }
        }
    }

    // Função para desenhar um botão de captura de tecla com estilo melhorado
    inline void DrawKeyCaptureButton(const char* label, const char* currentKeyName, KeyCaptureType captureType) {
        // Criar um ID único para o botão
        ImGui::PushID(label);

        // Determinar a cor do botão com base no estado de captura
        ImVec4 buttonColor = IsCapturing && CurrentCaptureType == captureType
            ? KeyCaptureColors::ACCENT_COLOR  // Cor de destaque quando capturando
            : KeyCaptureColors::HEADER_COLOR; // Cor padrão do botão

        // Aplicar estilo melhorado para o botão
        ImGui::PushStyleColor(ImGuiCol_Button, buttonColor);
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(buttonColor.x + 0.1f, buttonColor.y + 0.1f, buttonColor.z + 0.1f, buttonColor.w));
        ImGui::PushStyleColor(ImGuiCol_ButtonActive, KeyCaptureColors::ACCENT_COLOR);
        ImGui::PushStyleColor(ImGuiCol_Text, KeyCaptureColors::TEXT_COLOR);
        ImGui::PushStyleVar(ImGuiStyleVar_FrameRounding, 4.0f); // Bordas arredondadas
        ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(6, 6)); // Padding maior

        // Texto do botão
        std::string buttonText = IsCapturing && CurrentCaptureType == captureType
            ? "Pressione qualquer tecla..."
            : currentKeyName;

        // Desenhar o botão
        if (ImGui::Button(buttonText.c_str(), ImVec2(140, 0))) {
            if (!IsCapturing) {
                StartCapture(captureType);
            } else {
                EndCapture();
            }
        }

        // Mostrar tooltip ao passar o mouse
        if (ImGui::IsItemHovered() && !IsCapturing) {
            ImGui::BeginTooltip();
            ImGui::Text("Clique para capturar uma nova tecla");
            ImGui::EndTooltip();
        }

        // Adicionar botão de limpar ao lado
        ImGui::SameLine();
        ImGui::PushID((std::string(label) + "_clear").c_str());
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.5f, 0.1f, 0.1f, 0.8f)); // Vermelho escuro
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.7f, 0.2f, 0.2f, 0.9f)); // Vermelho mais claro
        ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.9f, 0.3f, 0.3f, 1.0f)); // Vermelho ainda mais claro

        if (ImGui::Button("X", ImVec2(30, 0))) {
            ClearShortcut(captureType);
        }

        if (ImGui::IsItemHovered()) {
            ImGui::BeginTooltip();
            ImGui::Text("Limpar atalho");
            ImGui::EndTooltip();
        }

        ImGui::PopStyleColor(3);
        ImGui::PopID();

        // Restaurar o estilo
        ImGui::PopStyleVar(2);
        ImGui::PopStyleColor(4);
        ImGui::PopID();
    }

    // Função para desenhar um par de botões de captura (teclado/mouse e gamepad) lado a lado
    inline void DrawKeyCaptureButtonPair(const char* label,
                                        const char* keyboardKeyName, KeyCaptureType keyboardCaptureType,
                                        const char* gamepadButtonName, KeyCaptureType gamepadCaptureType,
                                        const char* description = nullptr) {
        // Título da linha
        ImGui::TextColored(KeyCaptureColors::TEXT_COLOR, "%s:", label);
        ImGui::Spacing();

        // Usar duas colunas para os botões
        ImGui::Columns(2, NULL, false);

        // Coluna 1: Botão de teclado/mouse
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "Teclado/Mouse:");
        DrawKeyCaptureButton((std::string(label) + "_Keyboard").c_str(), keyboardKeyName, keyboardCaptureType);

        ImGui::NextColumn();

        // Coluna 2: Botão de gamepad
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "Gamepad:");
        DrawKeyCaptureButton((std::string(label) + "_Gamepad").c_str(), gamepadButtonName, gamepadCaptureType);

        ImGui::Columns(1);

        // Adicionar descrição se fornecida
        if (description != nullptr) {
            ImGui::TextWrapped("%s", description);
        }

        ImGui::Separator();
    }
}