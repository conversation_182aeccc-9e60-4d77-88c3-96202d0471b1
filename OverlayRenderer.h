#pragma once
// #include "../Custom/Utils.h"
#include "Globals.h"
#include <cmath> // Certifique-se de incluir a biblioteca cmath
#include "CheatModules.h"

// Constantes para evitar strings literais
const FString HEAD_SOCKET = L"head";
const FString NECK_SOCKET = L"neck_01";

void DrawTransition(UWorld *aWorld, UEngine *Engine, ImDrawList *BackgroundList, ImDrawList *ForegroundList)
{
    // Usar o sistema de segurança para executar a função principal
    SafetySystem::SafeExecute("DrawTransitionCritical", [&]()
                              {
        // Verificação robusta de ponteiros
        if (!IsValidObjectPtr(aWorld) || !IsValidObjectPtr(Engine))
        {
            SafetySystem::LogError("DrawTransitionCritical", "Ponteiros iniciais inválidos (aWorld ou Engine)");
            return;
        }

        UWorld *World = aWorld;
        Variables::World = World;

        //---------------------------------------------------------------------
        // 		🛡️	INTERRUPTOR GLOBAL: Verificação de Game State
        //---------------------------------------------------------------------
        // Verificar o estado do jogo SEMPRE (não pode ser bloqueado)
        static SDK::EMatchState lastMatchState = SDK::EMatchState::Pending;
        SDK::EMatchState currentMatchState = SDK::EMatchState::Pending;

        // Obter o estado atual do jogo usando métodos do SDK
        try
        {
            // Usar UMarvelBlueprintLibrary para obter o estado do jogo
            SDK::EMatchState outState;
            if (SDK::UMarvelBlueprintLibrary::GetMatchState(World, &outState))
            {
                currentMatchState = outState;
            }
            else
            {
                SafetySystem::LogError("DrawTransitionCritical", "GetMatchState retornou false");
            }
        }
        catch (...)
        {
            // Se falhar, assumir estado não-Fighting para segurança
            SafetySystem::LogError("DrawTransitionCritical", "EXCEÇÃO em GetMatchState - assumindo Pending");
            currentMatchState = SDK::EMatchState::Pending;
        }

        // 🚨 INTERRUPTOR GLOBAL: APENAS Fighting permite execução dos cheats
        bool allowCheatExecution = (currentMatchState == SDK::EMatchState::Fighting);

        // Atualizar o último estado (sem log informativo)
        if (currentMatchState != lastMatchState)
        {
            lastMatchState = currentMatchState;
        }

        // ❌ SE NÃO FOR Fighting, BLOQUEAR TODA EXECUÇÃO DE CHEATS
        if (!allowCheatExecution)
        {
            // Apenas permitir verificação de estado - NENHUM cheat é executado
            return;
        }

        //---------------------------------------------------------------------
        // 		💀	VERIFICAÇÃO ADICIONAL: Local Player Alive
        //---------------------------------------------------------------------
        // Verificar se o jogador local está vivo (proteção adicional)
        bool isLocalPlayerAlive = false;
        try
        {
            // Obter o PlayerController local usando método correto do SDK
            SDK::AMarvelPlayerController* PlayerController = SDK::UMarvelBlueprintLibrary::GetLocalMarvelPlayerController(World);
            if (IsValidObjectPtr(PlayerController))
            {
                // Método 1: Verificar através do Pawn usando método estático
                SDK::APlayerState* PlayerState = SDK::UMarvelBlueprintLibrary::GetPlayerState(PlayerController);
                SDK::AMarvelPlayerState* MarvelPlayerState = static_cast<SDK::AMarvelPlayerState*>(PlayerState);
                if (IsValidObjectPtr(MarvelPlayerState))
                {
                    // Usar propriedades diretas do PlayerState
                    isLocalPlayerAlive = MarvelPlayerState->bIsAlive && !MarvelPlayerState->IsDead();
                }

                // Método 2: Verificar através do Pawn (backup)
                if (!isLocalPlayerAlive)
                {
                    // Usar método estático para obter o character
                    SDK::AMarvelBaseCharacter* LocalCharacter = SDK::UMarvelBlueprintLibrary::GetMarvelBaseCharacter(World, 0);
                    if (IsValidObjectPtr(LocalCharacter))
                    {
                        // Usar método IsAlive() do SDK
                        isLocalPlayerAlive = LocalCharacter->IsAlive();

                        // Verificação adicional por Health
                        if (isLocalPlayerAlive)
                        {
                            float currentHealth = LocalCharacter->GetCurrentHealth();
                            isLocalPlayerAlive = (currentHealth > 0.0f);
                        }
                    }
                }
            }
        }
        catch (...)
        {
            // Se falhar, assumir morto para segurança
            isLocalPlayerAlive = false;
        }

        // 🚨 INTERRUPTOR ADICIONAL: APENAS se jogador estiver vivo
        if (!isLocalPlayerAlive)
        {
            // Se jogador estiver morto, não executar cheats
            return;
        }

        // ✅ APENAS AQUI (Fighting state + Player Alive) os cheats podem ser executados

        // Inicializar o ThreatValueAdmin para a funcionalidade de Ultimate Charge
        if (!Variables::ThreatValueAdmin && IsValidObjectPtr(Variables::World))
        {
            // Usar try-catch para evitar crashes ao acessar GetThreatValueAdmin
            try
            {
                Variables::ThreatValueAdmin = SDK::UMarvelAudioLibrary::GetThreatValueAdmin(Variables::World);
            }
            catch (...)
            {
                SafetySystem::LogError("DrawTransition", "Falha ao obter ThreatValueAdmin");
            }
        }

        // Obter o PlayerController com verificação de segurança
        APlayerController *PlayerController = nullptr;
        if (IsValidObjectPtr(World) && IsValidObjectPtr(UGameplayStatics::StaticClass()))
        {
            PlayerController = UGameplayStatics::GetPlayerController(World, 0);
        }

        if (!IsValidObjectPtr(PlayerController))
            return;

        Variables::PlayerController = PlayerController;

        // Obter o PlayerCameraManager com verificação de segurança
        APlayerCameraManager *PlayerCameraManager = nullptr;
        if (IsValidObjectPtr(World) && IsValidObjectPtr(UGameplayStatics::StaticClass()))
        {
            PlayerCameraManager = UGameplayStatics::GetPlayerCameraManager(World, 0);
        }

        if (!IsValidObjectPtr(PlayerCameraManager))
            return;

        // Verificar o AcknowledgedPawn com segurança
        APawn *AcknowledgedPawn = nullptr;
        if (IsValidObjectPtr(PlayerController))
        {
            AcknowledgedPawn = PlayerController->AcknowledgedPawn;
        }

        if (!IsValidObjectPtr(Variables::AcknowledgedPawn) || Variables::AcknowledgedPawn != AcknowledgedPawn)
        {
            Variables::AcknowledgedPawn = AcknowledgedPawn;
            if (!IsValidObjectPtr(Variables::AcknowledgedPawn))
                return;
        }

        // Obter informações da câmera com segurança
        if (IsValidObjectPtr(PlayerCameraManager))
        {
            Variables::CameraLocation = PlayerCameraManager->GetCameraLocation();
            Variables::CameraRotation = PlayerCameraManager->GetCameraRotation();
        }

        // Alterar FOV se necessário
        if (mods::fov_changer && IsValidObjectPtr(PlayerController))
        {
            PlayerController->FOV(mods::fov_changer_amount);
        }

        // Verificar se estamos realmente em jogo
        bool inGame = false;
        SafetySystem::SafeExecute("IsInGameCheck", [&]()
                                  { inGame = CheatFeatures::IsInGame(); });

        // Calcular círculos de FOV com segurança
        const float actualFovCircle = mods::actualfovcircle = (mods::fov * Variables::ScreenSize.X / mods::fov_changer_amount) / 2.0f;
        if (mods::aimbotFovCircle && mods::actualfovcircle > 0 && ImGui::GetForegroundDrawList())
        {
            ImGui::GetForegroundDrawList()->AddCircle(
                ImVec2(Variables::ScreenCenter.X, Variables::ScreenCenter.Y),
                actualFovCircle,
                ImColor(255, 255, 255));
        }

        const float actualBulletTPFovCircle = mods::actual_bullet_tp_fovcircle = (mods::bullet_tp_fov * Variables::ScreenSize.X / mods::fov_changer_amount) / 2.0f;
        if (mods::bullet_tp_fov_circle && mods::actual_bullet_tp_fovcircle > 0 && ImGui::GetForegroundDrawList())
        {
            ImGui::GetForegroundDrawList()->AddCircle(
                ImVec2(Variables::ScreenCenter.X, Variables::ScreenCenter.Y),
                actualBulletTPFovCircle,
                ImColor(255, 255, 0));
        }

        // Converter nomes de sockets com segurança
        FName HeadSocketName, NeckSocketName;
        if (IsValidObjectPtr(UKismetStringLibrary::StaticClass()))
        {
            HeadSocketName = UKismetStringLibrary::Conv_StringToName(HEAD_SOCKET);
            NeckSocketName = UKismetStringLibrary::Conv_StringToName(NECK_SOCKET);
        }

        // Limitar a taxa de atualização para reduzir a carga
        static int frameCounter = 0;
        frameCounter++;
        if (frameCounter % 2 != 0)
        {
            Sleep(1);
            return;
        }

        // Obter a lista de atores com segurança
        TArray<AActor *> ActorList;
        if (IsValidObjectPtr(World) && IsValidObjectPtr(UGameplayStatics::StaticClass()) &&
            IsValidObjectPtr(AMarvelBaseCharacter::StaticClass()))
        {
            UGameplayStatics::GetAllActorsOfClass(World, AMarvelBaseCharacter::StaticClass(), &ActorList);
        }
        else
        {
            return;
        }

        if (ActorList.Num() <= 1)
        {
            ActorList.Clear();
            return;
        }

        // Não precisamos mais detectar novos heróis

        // ESP e Glow podem ser executados sempre que houver jogadores, independentemente do estado do jogo
        if (!SafetySystem::IsSafeMode())
        {
            // Executar ESP se estiver ativado
            if (mods::esp)
            {
                // Verificar se o BackgroundList é válido usando safe_memory_check
                if (!IsValidPtr(BackgroundList))
                {
                    SafetySystem::LogError("OverlayRenderer", "BackgroundList is invalid");
                }
                else
                {
                    // Adicionar um texto de debug para verificar se o BackgroundList está funcionando
                    BackgroundList->AddText(ImVec2(100, 100), IM_COL32(255, 0, 0, 255), "ESP DEBUG TEXT");

                    SafetySystem::SafeExecute("DrawESP", [&]()
                                              { CheatFeatures::DrawESP(ActorList, PlayerController, BackgroundList, HeadSocketName); });
                }
            }

            // Aplicar o efeito de Glow nos jogadores
            if (mods::enableGlow)
            {
                SafetySystem::SafeExecute("GlowRoutine", [&]()
                                          { CheatFeatures::GlowRoutine(PlayerController, ActorList); });
            }
        }

        // Só executar funções que dependem do estado do jogo se estiver em jogo
        if (inGame && !SafetySystem::IsSafeMode())
        {
            // Verificar a tecla de Team Target Mode
            SafetySystem::SafeExecute("CheckTeamTargetKey", [&]()
                                      { CheatFeatures::CheckTeamTargetKey(PlayerController); });

            // Selecionar alvo para aimbot/bulletTP
            AMarvelBaseCharacter *TargetPlayer = nullptr;
            if (mods::aimbot || mods::bullet_tp)
            {
                SafetySystem::SafeExecute("SelectTarget", [&]()
                                          { TargetPlayer = CheatFeatures::SelectTarget(ActorList, PlayerController, HeadSocketName, actualFovCircle); });
            }

            // Executar Aimbot se ativado
            if (mods::aimbot && IsValidObjectPtr(TargetPlayer))
            {
                SafetySystem::SafeExecute("RunAimbot", [&]()
                                          { CheatFeatures::RunAimbot(World, PlayerController, PlayerCameraManager, TargetPlayer, HeadSocketName, actualFovCircle); });
            }

            // Executar BulletTP se ativado
            if (mods::bullet_tp && IsValidObjectPtr(TargetPlayer))
            {
                SafetySystem::SafeExecute("RunBulletTP", [&]()
                                          { CheatFeatures::RunBulletTP(World, PlayerController, PlayerCameraManager, TargetPlayer, HeadSocketName, NeckSocketName, mods::actual_bullet_tp_fovcircle); });
            }
        }

        // Limpar a lista de atores
        ActorList.Clear();

        // Atualizar o último pawn reconhecido
        static APawn *LastAcknowledgedPawn = nullptr;
        if (LastAcknowledgedPawn != AcknowledgedPawn)
        {
        LastAcknowledgedPawn = AcknowledgedPawn;
        } });
}