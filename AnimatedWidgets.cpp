#include "AnimatedWidgets.h"
#include "ImGui/imgui_internal.h"

// Constantes de cores para animações
namespace AnimationColors {
    const ImVec4 TAB_INDICATOR = ImVec4(1.0f, 0.7f, 0.2f, 1.0f); // Cor do indicador de aba ativa
}

bool AnimatedWidgets::AnimatedCheckbox(const char *label, bool *v)
{
    ImGuiWindow *window = ImGui::GetCurrentWindow();
    if (window->SkipItems)
        return false;

    ImGuiContext &g = *GImGui;
    const ImGuiStyle &style = g.Style;
    const ImGuiID id = window->GetID(label);
    const ImVec2 label_size = ImGui::CalcTextSize(label, NULL, true);

    const float square_sz = ImGui::GetFrameHeight();
    const ImVec2 pos = window->DC.CursorPos;

    // Criar o retângulo total usando ImVec2 separados
    ImVec2 size(square_sz + (label_size.x > 0.0f ? style.ItemInnerSpacing.x + label_size.x : 0.0f),
                label_size.y + style.FramePadding.y * 2.0f);
    const ImRect total_bb(pos, ImVec2(pos.x + size.x, pos.y + size.y));

    ImGui::ItemSize(total_bb, style.FramePadding.y);
    if (!ImGui::ItemAdd(total_bb, id))
        return false;

    bool hovered, held;
    bool pressed = ImGui::ButtonBehavior(total_bb, id, &hovered, &held);
    if (pressed)
    {
        *v = !(*v);
        ImGui::MarkItemEdited(id);
    }

    // Obter ou criar o estado de animação para este checkbox
    auto &animation = m_checkboxAnimations[id];

    // Verificar se o estado mudou
    if (animation.lastState != *v)
    {
        animation.lastState = *v;
        animation.lastUpdateTime = std::chrono::steady_clock::now();
    }

    // Calcular o progresso da animação
    auto currentTime = std::chrono::steady_clock::now();
    float deltaTime = std::chrono::duration<float>(currentTime - animation.lastUpdateTime).count();

    if (*v)
    {
        animation.progress += deltaTime * CHECKBOX_ANIMATION_SPEED;
        if (animation.progress > 1.0f)
            animation.progress = 1.0f;
    }
    else
    {
        animation.progress -= deltaTime * CHECKBOX_ANIMATION_SPEED;
        if (animation.progress < 0.0f)
            animation.progress = 0.0f;
    }

    animation.lastUpdateTime = currentTime;

    // Renderizar o checkbox com animação
    ImVec2 check_min = pos;
    ImVec2 check_max = ImVec2(pos.x + square_sz, pos.y + square_sz);
    const ImRect check_bb(check_min, check_max);

    // Cores para o checkbox
    ImU32 bg_col = ImGui::GetColorU32((held && hovered) ? ImGuiCol_FrameBgActive : hovered ? ImGuiCol_FrameBgHovered
                                                                                           : ImGuiCol_FrameBg);
    ImU32 border_col = ImGui::GetColorU32(ImGuiCol_Border);
    ImU32 check_col = ImGui::GetColorU32(ImGuiCol_CheckMark);
    ImU32 fill_col = ImGui::GetColorU32(ImGuiCol_FrameBgActive);

    // Desenhar o fundo do checkbox com borda
    window->DrawList->AddRectFilled(check_bb.Min, check_bb.Max, bg_col, style.FrameRounding);
    window->DrawList->AddRect(check_bb.Min, check_bb.Max, border_col, style.FrameRounding);

    // Calcular o tamanho do preenchimento animado
    if (animation.progress > 0.0f)
    {
        // Calcular o tamanho do preenchimento com base no progresso da animação
        float pad = square_sz * 0.15f;
        ImVec2 fill_min = ImVec2(check_bb.Min.x + pad, check_bb.Min.y + pad);
        ImVec2 fill_max = ImVec2(check_bb.Max.x - pad, check_bb.Max.y - pad);

        // Interpolar o tamanho do preenchimento com efeito de bounce
        float progress_adjusted = animation.progress;
        // Implementação manual do efeito de bounce
        if (progress_adjusted < 4 / 11.0f)
        {
            progress_adjusted = (121 * progress_adjusted * progress_adjusted) / 16.0f;
        }
        else if (progress_adjusted < 8 / 11.0f)
        {
            progress_adjusted = (363 / 40.0f * progress_adjusted * progress_adjusted) - (99 / 10.0f * progress_adjusted) + 17 / 5.0f;
        }
        else if (progress_adjusted < 9 / 10.0f)
        {
            progress_adjusted = (4356 / 361.0f * progress_adjusted * progress_adjusted) - (35442 / 1805.0f * progress_adjusted) + 16061 / 1805.0f;
        }
        else
        {
            progress_adjusted = (54 / 5.0f * progress_adjusted * progress_adjusted) - (513 / 25.0f * progress_adjusted) + 268 / 25.0f;
        }

        // Efeito de preenchimento
        window->DrawList->AddRectFilled(
            fill_min,
            ImVec2(fill_min.x + (fill_max.x - fill_min.x) * progress_adjusted,
                   fill_min.y + (fill_max.y - fill_min.y) * progress_adjusted),
            fill_col,
            style.FrameRounding * 0.8f);

        // Desenhar o checkmark com fade-in
        if (animation.progress > 0.5f)
        {
            float checkmark_alpha = (animation.progress - 0.5f) * 2.0f; // 0.0 a 1.0
            ImU32 check_col_alpha = ImGui::GetColorU32(ImVec4(
                ImGui::ColorConvertU32ToFloat4(check_col).x,
                ImGui::ColorConvertU32ToFloat4(check_col).y,
                ImGui::ColorConvertU32ToFloat4(check_col).z,
                ImGui::ColorConvertU32ToFloat4(check_col).w * checkmark_alpha));

            // Desenhar o checkmark
            const float check_sz = square_sz * 0.5f;
            const ImVec2 center = ImVec2(check_bb.Min.x + square_sz * 0.5f, check_bb.Min.y + square_sz * 0.5f);
            const float thickness = std::max(square_sz / 10.0f, 1.0f);

            // Desenhar um checkmark estilizado
            window->DrawList->AddLine(
                ImVec2(center.x - check_sz * 0.3f, center.y),
                ImVec2(center.x - check_sz * 0.1f, center.y + check_sz * 0.3f),
                check_col_alpha,
                thickness);
            window->DrawList->AddLine(
                ImVec2(center.x - check_sz * 0.1f, center.y + check_sz * 0.3f),
                ImVec2(center.x + check_sz * 0.4f, center.y - check_sz * 0.3f),
                check_col_alpha,
                thickness);
        }
    }

    // Desenhar o texto do label
    if (label_size.x > 0.0f)
    {
        ImGui::PushStyleColor(ImGuiCol_Text, ImGui::GetColorU32(ImGuiCol_Text));
        ImGui::RenderText(ImVec2(check_bb.Max.x + style.ItemInnerSpacing.x, check_bb.Min.y + style.FramePadding.y), label);
        ImGui::PopStyleColor();
    }

    return pressed;
}

void AnimatedWidgets::BeginTabBarAnimated(const char *str_id)
{
    // Iniciar a barra de abas normal
    ImGui::BeginTabBar(str_id, ImGuiTabBarFlags_None);

    // Armazenar o ID da barra de abas atual
    m_currentTabBarId = ImGui::GetID(str_id);
}

bool AnimatedWidgets::BeginTabItemAnimated(const char *label, bool *p_open, ImGuiTabItemFlags flags)
{
    // Verificar se temos uma barra de abas válida
    if (m_currentTabBarId == 0)
        return ImGui::BeginTabItem(label, p_open, flags);

    // Obter o ID da aba
    ImGuiID tab_id = ImGui::GetID(label);

    // Obter ou criar o estado de animação para esta aba
    auto &animation = m_tabAnimations[tab_id];

    // Verificar se a aba estava ativa anteriormente
    animation.wasActive = animation.isActive;

    // Iniciar a aba normal
    bool is_active = ImGui::BeginTabItem(label, p_open, flags);

    // Verificar se o estado mudou
    if (animation.isActive != is_active)
    {
        animation.isActive = is_active;
        animation.lastUpdateTime = std::chrono::steady_clock::now();
    }

    // Calcular o progresso da animação
    auto currentTime = std::chrono::steady_clock::now();
    float deltaTime = std::chrono::duration<float>(currentTime - animation.lastUpdateTime).count();

    // Animação da barra indicadora
    if (is_active)
    {
        animation.progress += deltaTime * TAB_ANIMATION_SPEED;
        if (animation.progress > 1.0f)
            animation.progress = 1.0f;
    }
    else
    {
        animation.progress -= deltaTime * TAB_ANIMATION_SPEED;
        if (animation.progress < 0.0f)
            animation.progress = 0.0f;
    }

    // Animação de fade do conteúdo
    if (is_active)
    {
        animation.contentAlpha += deltaTime * TAB_CONTENT_FADE_SPEED;
        if (animation.contentAlpha > 1.0f)
            animation.contentAlpha = 1.0f;
    }
    else
    {
        animation.contentAlpha -= deltaTime * TAB_CONTENT_FADE_SPEED;
        if (animation.contentAlpha < 0.0f)
            animation.contentAlpha = 0.0f;
    }

    animation.lastUpdateTime = currentTime;

    // Aplicar efeito de animação se a aba estiver ativa ou em transição
    ImGuiWindow *window = ImGui::GetCurrentWindow();

    // Animação da barra indicadora
    if (animation.progress > 0.0f && is_active)
    {
        // Desenhar um indicador animado na parte inferior da aba
        // Como não temos acesso direto às propriedades internas da aba,
        // vamos usar uma abordagem simplificada

        // Obter a largura da aba (estimativa)
        float tab_width = ImGui::CalcTextSize(label).x + 20.0f; // 20 pixels de padding

        // Obter a posição atual do cursor
        ImVec2 cursor_pos = ImGui::GetCursorPos();

        // Calcular a posição da linha animada
        const float line_thickness = 3.0f;
        const float animation_width = tab_width * animation.progress;

        // Calcular a posição Y da linha (logo abaixo da aba)
        float line_y = cursor_pos.y - line_thickness - 2.0f;

        // Calcular a posição X central
        float center_x = cursor_pos.x - tab_width * 0.5f;
        float start_x = center_x - animation_width * 0.5f;

        // Desenhar a linha com cor laranja brilhante
        window->DrawList->AddRectFilled(
            ImVec2(start_x, line_y),
            ImVec2(start_x + animation_width, line_y + line_thickness),
            ImGui::GetColorU32(AnimationColors::TAB_INDICATOR),
            0.0f);
    }

    // Aplicar efeito de fade ao conteúdo da aba
    if (is_active && animation.contentAlpha < 1.0f)
    {
        // Salvar a cor de texto atual
        ImVec4 textColor = ImGui::GetStyleColorVec4(ImGuiCol_Text);

        // Aplicar alfa ao texto e elementos da aba
        ImGui::PushStyleVar(ImGuiStyleVar_Alpha, animation.contentAlpha);
    }

    return is_active;
}

void AnimatedWidgets::EndTabBarAnimated()
{
    // Verificar se há alguma aba com animação de fade ativa
    for (auto &pair : m_tabAnimations)
    {
        if (pair.second.isActive && pair.second.contentAlpha < 1.0f)
        {
            // Restaurar o estilo após o efeito de fade
            ImGui::PopStyleVar();
            break; // Só deve haver uma aba ativa por vez
        }
    }

    // Finalizar a barra de abas normal
    ImGui::EndTabBar();

    // Resetar o ID da barra de abas atual
    m_currentTabBarId = 0;
}

//---------------------------------------------------------------------
// 		🎬	UI Entry Animation System
//---------------------------------------------------------------------
void AnimatedWidgets::BeginUIAnimation(bool isVisible)
{
    // Verificar se o estado mudou
    if (m_uiAnimation.isVisible != isVisible)
    {
        m_uiAnimation.wasVisible = m_uiAnimation.isVisible;
        m_uiAnimation.isVisible = isVisible;
        m_uiAnimation.lastUpdateTime = std::chrono::steady_clock::now();

        if (!isVisible)
        {
            m_uiAnimation.isExiting = true;
            m_uiAnimation.exitProgress = 0.0f;
        }
        else
        {
            m_uiAnimation.isExiting = false;
            m_uiAnimation.entryProgress = 0.0f;
        }
    }

    // Calcular o progresso da animação
    auto currentTime = std::chrono::steady_clock::now();
    float deltaTime = std::chrono::duration<float>(currentTime - m_uiAnimation.lastUpdateTime).count();

    if (isVisible && !m_uiAnimation.isExiting)
    {
        // Animação de entrada
        m_uiAnimation.entryProgress += deltaTime * UI_ENTRY_ANIMATION_SPEED;
        if (m_uiAnimation.entryProgress > 1.0f)
            m_uiAnimation.entryProgress = 1.0f;
    }
    else if (m_uiAnimation.isExiting)
    {
        // Animação de saída
        m_uiAnimation.exitProgress += deltaTime * UI_EXIT_ANIMATION_SPEED;
        if (m_uiAnimation.exitProgress > 1.0f)
        {
            m_uiAnimation.exitProgress = 1.0f;
            m_uiAnimation.isExiting = false;
        }
    }

    m_uiAnimation.lastUpdateTime = currentTime;
}

float AnimatedWidgets::GetUIAnimationProgress()
{
    if (m_uiAnimation.isExiting)
    {
        return 1.0f - m_uiAnimation.exitProgress; // Fade out
    }
    else if (m_uiAnimation.isVisible)
    {
        return m_uiAnimation.entryProgress; // Fade in
    }
    return 0.0f;
}

bool AnimatedWidgets::ShouldRenderUI()
{
    return m_uiAnimation.isVisible || m_uiAnimation.isExiting;
}
