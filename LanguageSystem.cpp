#include "LanguageSystem.h"

void LanguageSystem::Initialize()
{
    // Definir os nomes dos idiomas
    m_languageNames[Language::English] = "English";
    m_languageNames[Language::Portuguese] = "Português";
    m_languageNames[Language::Spanish] = "Español";
    m_languageNames[Language::French] = "Français";
    m_languageNames[Language::Russian] = "Русский";
    m_languageNames[Language::Italian] = "Italiano";

    // Definir as strings em inglês
    auto &en = m_strings[Language::English];

    // Tooltips - Aimbot
    en[TextID::Aimbot] = "Activates the automatic aiming system that locks onto the closest target";
    en[TextID::Smoothing] = "Controls how smooth the aimbot movement is (higher = smoother)";
    en[TextID::SeparatePitchYawSmoothing] = "Enable separate smoothing control for vertical (Pitch) and horizontal (Yaw) movement";
    en[TextID::SmoothingPitch] = "Controls how smooth the vertical movement is (higher = smoother)";
    en[TextID::SmoothingYaw] = "Controls how smooth the horizontal movement is (higher = smoother)";
    en[TextID::FocusLowestHealth] = "Prioritizes targets with the lowest health instead of closest distance";
    en[TextID::ShowAimbotFOVCircle] = "Shows a circle on the screen that represents the aimbot's field of view";
    en[TextID::AimbotFOVRadius] = "Sets the size of the aimbot FOV circle radius";

    // Tooltips - Aimbot Advanced
    en[TextID::UseInertia] = "Enables inertia system for smoother aim transitions";
    en[TextID::InertiaFactor] = "Controls how gradual the aim movement is (higher = more gradual)";
    en[TextID::UseAimDelay] = "Adds a delay before aiming at a new target";
    en[TextID::AimDelayTime] = "Sets the delay time in seconds before aiming at a new target";
    en[TextID::UseAdaptiveMovement] = "Enables adaptive movement that adjusts smoothing while tracking the same target";
    en[TextID::AdaptiveDuration] = "Time in seconds to reach minimum values while tracking the same target";
    en[TextID::InitialSmoothing] = "Initial smoothing value when first aiming at a target";
    en[TextID::MinimumSmoothing] = "Minimum smoothing value after tracking the same target for the adaptive duration";
    en[TextID::SeparateAdaptiveSettings] = "Enable separate adaptive settings for vertical (Pitch) and horizontal (Yaw) movement";
    en[TextID::InitialPitchSmoothing] = "Initial pitch smoothing value when first aiming at a target";
    en[TextID::MinimumPitchSmoothing] = "Minimum pitch smoothing value after tracking the same target";
    en[TextID::InitialYawSmoothing] = "Initial yaw smoothing value when first aiming at a target";
    en[TextID::MinimumYawSmoothing] = "Minimum yaw smoothing value after tracking the same target";
    en[TextID::UseAdaptiveInertia] = "Enables adaptive inertia that adjusts inertia factor while tracking the same target";
    en[TextID::InitialInertiaFactor] = "Initial inertia factor when first aiming at a target";
    en[TextID::MinimumInertiaFactor] = "Minimum inertia factor after tracking the same target";

    // Tooltips - Flick System
    en[TextID::UseFlick] = "Enables autonomous Flick system that reduces smoothing and inertia to minimum values for fast aim movement when aimbot key is pressed";
    en[TextID::FlickDuration] = "Duration in seconds that Flick remains active after key press, reducing smoothing/inertia to minimum values (0.1 = very fast, 3.0 = long duration)";
    en[TextID::FlickReductionType] = "Type of reduction curve: Linear (gradual), Exponential (fast start, smooth end), Quadratic (smooth start, fast end)";

    // Tooltips - Bullet TP
    en[TextID::BulletTP] = "Activates the bullet teleportation system to automatically hit targets";
    en[TextID::ShowBulletTPFOVCircle] = "Shows a circle on the screen that represents the Bullet TP's field of view";
    en[TextID::BulletTPFOVRadius] = "Sets the size of the Bullet TP FOV circle radius";
    en[TextID::BulletTPTarget] = "Selects which part of the enemy to target with Bullet TP";
    en[TextID::MinSafeDistance] = "Minimum distance for safe bullet teleportation";
    en[TextID::MaxSafeDistance] = "Maximum distance for bullet teleportation adjustment";
    en[TextID::ContinueAfterKeyRelease] = "Continue teleporting bullets for a short time after releasing the key";
    en[TextID::ContinueDuration] = "How long to continue teleporting bullets after key release";
    en[TextID::HitPrecision] = "Precision of bullet teleportation (0.98 = 98% of distance to target)";
    en[TextID::DebugMode] = "Enable debug logging for BulletTP system";

    // Tooltips - Humanization
    en[TextID::TargetBodyPartMode] = "Selects which body part to target (Head, Neck, Chest, Spine, Random, or Humanized)";
    en[TextID::HeadProbability] = "Probability of targeting the head when using humanized mode";
    en[TextID::NeckProbability] = "Probability of targeting the neck when using humanized mode";
    en[TextID::ChestProbability] = "Probability of targeting the chest when using humanized mode";
    en[TextID::SpineProbability] = "Probability of targeting the spine when using humanized mode";
    en[TextID::BodyPartUpdateInterval] = "Time in seconds between body part changes in humanized mode";

    // Tooltips - Keybinds
    en[TextID::UseGamepad] = "Enables gamepad support for aimbot and bullet TP activation";
    en[TextID::AimbotKey] = "Sets the key used to activate the aimbot";
    en[TextID::BulletTPKey] = "Sets the key used to activate Bullet TP";

    // Tooltips - Checks
    en[TextID::SelfCheck] = "Prevents features from being applied to yourself";
    en[TextID::VisibleCheck] = "Checks if the target is visible before applying features";
    en[TextID::UseLineOfSight] = "Uses LineOfSight for better accuracy in visibility checks, detecting enemies behind walls more reliably";
    en[TextID::TeamCheck] = "Ignores teammates when using features like Aimbot, ESP, and Bullet TP";

    // Tooltips - ESP
    en[TextID::EnableESP] = "Enables all ESP features (health bars, distance, tracer lines, etc.)";
    en[TextID::TracerLines] = "Draws lines connecting the screen center to enemy players";
    en[TextID::ShowHealth] = "Shows the health amount of enemy players";
    en[TextID::ShowDistance] = "Shows the distance between you and enemy players";
    en[TextID::ShowUltimatePercentage] = "Shows the ultimate charge percentage of enemy players";
    en[TextID::EnableGlow] = "Activates the glow effect on enemy players, allowing you to see them through walls";

    // Tooltips - FOV
    en[TextID::FOVChanger] = "Changes the game's field of view for a wider perspective";
    en[TextID::FOVSlider] = "Sets the field of view value (higher = wider view)";

    // Tooltips - Config
    en[TextID::Language] = "Changes the language of the interface";
    en[TextID::SaveConfig] = "Saves the current configuration to a file";
    en[TextID::LoadConfig] = "Loads a configuration from a file";
    en[TextID::DeleteConfig] = "Deletes a saved configuration file";

    // Section Headers
    en[TextID::AimbotSettings] = "AIMBOT SETTINGS";
    en[TextID::SmoothingSettings] = "SMOOTHING SETTINGS";
    en[TextID::BulletTPSettings] = "BULLET TP SETTINGS";
    en[TextID::AdvancedAimbotSettings] = "ADVANCED AIMBOT SETTINGS";
    en[TextID::InertiaSettings] = "INERTIA SETTINGS";
    en[TextID::AdaptiveMovementSettings] = "ADAPTIVE MOVEMENT SETTINGS";
    en[TextID::HumanizationSettings] = "HUMANIZATION SETTINGS";

    en[TextID::KeybindSettings] = "KEYBIND SETTINGS";
    en[TextID::CheckSettings] = "CHECK SETTINGS";
    en[TextID::ESPSettings] = "ESP SETTINGS";
    en[TextID::FOVSettings] = "FOV SETTINGS";
    en[TextID::ConfigSettings] = "CONFIGURATION SETTINGS";
    en[TextID::AdvancedBulletTPSettings] = "ADVANCED BULLET TP SETTINGS";
    en[TextID::AvailableConfigurations] = "AVAILABLE CONFIGURATIONS";
    en[TextID::CurrentConfig] = "Current Config";

    // Menu Tabs
    en[TextID::TabAimbotBulletTP] = "AIMBOT & BULLET TP";
    en[TextID::TabVisuals] = "VISUALS";
    en[TextID::TabMisc] = "MISC";
    en[TextID::TabConfig] = "CONFIG";

    // Menu Items
    en[TextID::AimbotLabel] = "Aimbot";
    en[TextID::SmoothingLabel] = "Smoothing";
    en[TextID::SeparatePitchYawSmoothingLabel] = "Separate Pitch/Yaw Control";
    en[TextID::SmoothingPitchLabel] = "Pitch Smoothing (Vertical)";
    en[TextID::SmoothingYawLabel] = "Yaw Smoothing (Horizontal)";
    en[TextID::FocusLowestHealthLabel] = "Focus Lowest Health";
    en[TextID::ShowAimbotFOVCircleLabel] = "Show Aimbot FOV Circle";
    en[TextID::AimbotFOVRadiusLabel] = "Aimbot FOV Radius";

    // Advanced Aimbot Labels
    en[TextID::AdvancedAimbotSettingsLabel] = "Advanced Aimbot Settings";
    en[TextID::UseInertiaLabel] = "Use Inertia";
    en[TextID::InertiaFactorLabel] = "Inertia Factor";
    en[TextID::UseAimDelayLabel] = "Use Aim Delay";
    en[TextID::AimDelayTimeLabel] = "Delay Time (s)";
    en[TextID::UseAdaptiveMovementLabel] = "Use Adaptive Movement";
    en[TextID::AdaptiveDurationLabel] = "Adaptive Duration (s)";
    en[TextID::InitialSmoothingLabel] = "Initial Smoothing";
    en[TextID::MinimumSmoothingLabel] = "Minimum Smoothing";
    en[TextID::SeparateAdaptiveSettingsLabel] = "Separate Pitch/Yaw Adaptive Settings";
    en[TextID::InitialPitchSmoothingLabel] = "Initial Pitch Smoothing";
    en[TextID::MinimumPitchSmoothingLabel] = "Minimum Pitch Smoothing";
    en[TextID::InitialYawSmoothingLabel] = "Initial Yaw Smoothing";
    en[TextID::MinimumYawSmoothingLabel] = "Minimum Yaw Smoothing";
    en[TextID::UseAdaptiveInertiaLabel] = "Use Adaptive Inertia";
    en[TextID::InitialInertiaFactorLabel] = "Initial Inertia Factor";
    en[TextID::MinimumInertiaFactorLabel] = "Minimum Inertia Factor";

    // Flick System Labels
    en[TextID::FlickSettingsLabel] = "FLICK SETTINGS";
    en[TextID::UseFlickLabel] = "Use Flick";
    en[TextID::FlickDurationLabel] = "Flick Duration";
    en[TextID::FlickReductionTypeLabel] = "Reduction Type";

    // Humanization Labels
    en[TextID::HumanizationSettingsLabel] = "Humanization Settings";
    en[TextID::TargetBodyPartModeLabel] = "Target Body Part";
    en[TextID::ChestOption] = "Chest";
    en[TextID::SpineOption] = "Spine";
    en[TextID::HumanizedOption] = "Humanized";
    en[TextID::HeadProbabilityLabel] = "Head Probability";
    en[TextID::NeckProbabilityLabel] = "Neck Probability";
    en[TextID::ChestProbabilityLabel] = "Chest Probability";
    en[TextID::SpineProbabilityLabel] = "Spine Probability";
    en[TextID::BodyPartUpdateIntervalLabel] = "Update Interval (s)";

    // Bullet TP Labels
    en[TextID::BulletTPLabel] = "Bullet TP";
    en[TextID::ShowBulletTPFOVCircleLabel] = "Show Bullet TP FOV Circle";
    en[TextID::BulletTPFOVRadiusLabel] = "BulletTP FOV Radius";
    en[TextID::BulletTPTargetLabel] = "Bullet TP Target";
    en[TextID::HeadOption] = "Head";
    en[TextID::NeckOption] = "Neck";
    en[TextID::RandomOption] = "Random";
    en[TextID::AdvancedBulletTPSettingsLabel] = "Advanced BulletTP Settings";
    en[TextID::MinSafeDistanceLabel] = "Min Safe Distance";
    en[TextID::MaxSafeDistanceLabel] = "Max Safe Distance";
    en[TextID::ContinueAfterKeyReleaseLabel] = "Continue After Key Release";
    en[TextID::ContinueDurationLabel] = "Continue Duration (s)";
    en[TextID::HitPrecisionLabel] = "Hit Precision";
    en[TextID::DebugModeLabel] = "Debug Mode";

    en[TextID::UseGamepadLabel] = "Use Gamepad";
    en[TextID::AimbotKeyLabel] = "Aimbot Key";
    en[TextID::BulletTPKeyLabel] = "Bullet TP Key";
    en[TextID::SelfCheckLabel] = "Self Check";
    en[TextID::VisibleCheckLabel] = "Visible Check";
    en[TextID::UseLineOfSightLabel] = "Use LineOfSight (Better Accuracy)";
    en[TextID::TeamCheckLabel] = "Team Check";
    en[TextID::EnableESPLabel] = "Enable ESP";
    en[TextID::TracerLinesLabel] = "Tracer Lines";
    en[TextID::ShowHealthLabel] = "Health";
    en[TextID::ShowDistanceLabel] = "Distance";
    en[TextID::ShowUltimatePercentageLabel] = "Ultimate Percentage";
    en[TextID::EnableGlowLabel] = "Enable Glow";
    en[TextID::FOVChangerLabel] = "FOV Changer";
    en[TextID::FOVSliderLabel] = "FOV Slider";
    en[TextID::LanguageLabel] = "Language";
    en[TextID::SaveConfigLabel] = "Save Configuration";
    en[TextID::LoadConfigLabel] = "Load Configuration";
    en[TextID::DeleteConfigLabel] = "Delete Configuration";
    en[TextID::ConfigNameLabel] = "Config Name";
    en[TextID::SaveButtonLabel] = "Save";
    en[TextID::LoadButtonLabel] = "Load";
    en[TextID::DeleteButtonLabel] = "Delete";

    // Role Target
    en[TextID::RoleTargetSettings] = "ROLE TARGET SETTINGS";
    en[TextID::EnableRoleTargetLabel] = "Enable Role Target";
    en[TextID::TargetRoleLabel] = "Target Role";
    en[TextID::AllRolesOption] = "All Roles";
    en[TextID::TankOption] = "Tank";
    en[TextID::DamageOption] = "Damage";
    en[TextID::SupportOption] = "Support";

    // Tabs
    en[TextID::KeybindsAndChecksTab] = "KEYBINDS & CHECKS";

    // App Title
    en[TextID::AppTitle] = "Rivals UnKnoWn";

    // Config UI
    en[TextID::CurrentConfigLabel] = "Current Config:";
    en[TextID::RefreshListButton] = "Refresh List";
    en[TextID::AvailableConfigurationsLabel] = "Available Configurations:";
    en[TextID::SaveButton] = "Save";
    en[TextID::LoadButton] = "Load";
    en[TextID::DeleteButton] = "Delete";

    // Advanced Settings
    en[TextID::DelaySettingsLabel] = "DELAY SETTINGS";

    // Humanization System
    en[TextID::EnableHumanizedTargetingLabel] = "Enable Humanized Targeting";
    en[TextID::EnableHumanizedTargetingTooltip] = "Enable or disable the humanized targeting system";
    en[TextID::BodyPartProbabilitiesLabel] = "Body Part Probabilities (0.0-1.0):";
    en[TextID::ProbabilitiesZeroInfoLabel] = "Info: All probabilities are zero. Equal distribution will be used.";
    en[TextID::ProbabilitiesNormalizedInfoLabel] = "Info: Probabilities will be normalized (Total: %.2f)";
    en[TextID::DynamicTimingLabel] = "Dynamic Timing (seconds):";
    en[TextID::MinChangeTimeLabel] = "Min Change Time";
    en[TextID::MinChangeTimeTooltip] = "Minimum time between body part changes in humanized mode";
    en[TextID::MaxChangeTimeLabel] = "Max Change Time";
    en[TextID::MaxChangeTimeTooltip] = "Maximum time between body part changes in humanized mode";
    en[TextID::RandomModeInfoLabel] = "Note: Random mode uses variable intervals (2-4 seconds)";
    en[TextID::InitialValueFromInertiaLabel] = "Initial value: %.1f (from Inertia Factor)";
    en[TextID::InitialValueFromSmoothingLabel] = "Initial value: %.1f (from Smoothing)";
    en[TextID::InitialValueFromPitchSmoothingLabel] = "Initial value: %.1f (from Pitch Smoothing)";
    en[TextID::InitialValueFromYawSmoothingLabel] = "Initial value: %.1f (from Yaw Smoothing)";
    en[TextID::AdaptiveSmoothingLabel] = "Adaptive Smoothing";
    en[TextID::NoneKeyLabel] = "None";

    // Role Target Tooltips
    en[TextID::EnableRoleTargetTooltip] = "Enables targeting only specific hero roles (Tank, Damage, Support)";
    en[TextID::TargetRoleTooltip] = "Select which role to target with Aimbot and Bullet TP\nIf no target of selected role is found, will fallback in order: Support > Damage > Tank";

    // Support Settings
    en[TextID::SupportSettingsLabel] = "SUPPORT SETTINGS";

    // Team Target Mode
    en[TextID::TeamTargetModeLabel] = "Team Target Mode";
    en[TextID::TeamTargetModeTooltip] = "When enabled, targets allies instead of enemies. Useful for support players";
    en[TextID::TeamTargetKeyLabel] = "Team Target Key";
    en[TextID::TeamTargetKeyTooltip] = "Key to toggle between targeting enemies or allies";

    // Support Vision
    en[TextID::SupportVisionLabel] = "Support Vision (360° View)";
    en[TextID::SupportVisionTooltip] = "Ignores FOV restrictions, allowing you to target allies/enemies in any direction";

    // Flying Target Priority
    en[TextID::FlyingTargetPriorityLabel] = "Prioritize Flying Targets";
    en[TextID::FlyingTargetPriorityTooltip] = "Prioritizes targets that are in the air (jumping, flying, etc.)";
    en[TextID::FlyingVelocityThresholdLabel] = "Flying Velocity Threshold";
    en[TextID::FlyingVelocityThresholdTooltip] = "Minimum vertical velocity to consider a target as flying";

    // UI Zoom Settings
    en[TextID::UIZoomSettings] = "UI ZOOM SETTINGS";
    en[TextID::EnableUIZoom] = "Enables UI zooming with CTRL+Mouse Wheel";
    en[TextID::EnableUIZoomLabel] = "Enable UI Zoom";
    en[TextID::UIZoomFactor] = "Sets the zoom level for the user interface";
    en[TextID::UIZoomFactorLabel] = "UI Zoom Factor";
    en[TextID::ResetZoomButton] = "Reset Zoom";

    // Profile Management
    en[TextID::UpdateCurrentProfileButton] = "Update Current Profile";
    en[TextID::UpdateCurrentProfileTooltip] = "Saves current settings to the active profile";

    // Definir as strings em português
    auto &pt = m_strings[Language::Portuguese];

    // Tooltips - Aimbot
    pt[TextID::Aimbot] = "Ativa o sistema de mira automática que trava no alvo mais próximo";
    pt[TextID::Smoothing] = "Controla quão suave é o movimento do aimbot (maior = mais suave)";
    pt[TextID::SeparatePitchYawSmoothing] = "Ativa o controle separado de suavização para movimento vertical (Pitch) e horizontal (Yaw)";
    pt[TextID::SmoothingPitch] = "Controla quão suave é o movimento vertical do aimbot (maior = mais suave)";
    pt[TextID::SmoothingYaw] = "Controla quão suave é o movimento horizontal do aimbot (maior = mais suave)";
    pt[TextID::FocusLowestHealth] = "Prioriza alvos com menor saúde em vez da menor distância";
    pt[TextID::ShowAimbotFOVCircle] = "Mostra um círculo na tela que representa a área de atuação do aimbot";
    pt[TextID::AimbotFOVRadius] = "Define o tamanho do raio do círculo de FOV do aimbot";

    // Tooltips - Aimbot Advanced
    pt[TextID::UseInertia] = "Ativa o sistema de inércia para transições de mira mais suaves";
    pt[TextID::InertiaFactor] = "Controla quão gradual é o movimento da mira (maior = mais gradual)";
    pt[TextID::UseAimDelay] = "Adiciona um atraso antes de mirar em um novo alvo";
    pt[TextID::AimDelayTime] = "Define o tempo de atraso em segundos antes de mirar em um novo alvo";
    pt[TextID::UseAdaptiveMovement] = "Ativa o movimento adaptativo que ajusta a suavização enquanto rastreia o mesmo alvo";
    pt[TextID::AdaptiveDuration] = "Tempo em segundos para atingir os valores mínimos enquanto rastreia o mesmo alvo";
    pt[TextID::InitialSmoothing] = "Valor inicial de suavização ao mirar pela primeira vez em um alvo";
    pt[TextID::MinimumSmoothing] = "Valor mínimo de suavização após rastrear o mesmo alvo pela duração adaptativa";
    pt[TextID::SeparateAdaptiveSettings] = "Ativa configurações adaptativas separadas para movimento vertical (Pitch) e horizontal (Yaw)";
    pt[TextID::InitialPitchSmoothing] = "Valor inicial de suavização de pitch ao mirar pela primeira vez em um alvo";
    pt[TextID::MinimumPitchSmoothing] = "Valor mínimo de suavização de pitch após rastrear o mesmo alvo";
    pt[TextID::InitialYawSmoothing] = "Valor inicial de suavização de yaw ao mirar pela primeira vez em um alvo";
    pt[TextID::MinimumYawSmoothing] = "Valor mínimo de suavização de yaw após rastrear o mesmo alvo";
    pt[TextID::UseAdaptiveInertia] = "Ativa a inércia adaptativa que ajusta o fator de inércia enquanto rastreia o mesmo alvo";
    pt[TextID::InitialInertiaFactor] = "Fator de inércia inicial ao mirar pela primeira vez em um alvo";
    pt[TextID::MinimumInertiaFactor] = "Fator de inércia mínimo após rastrear o mesmo alvo";

    // Tooltips - Sistema Flick
    pt[TextID::UseFlick] = "Ativa o sistema Flick autônomo que reduz suavização e inércia aos valores mínimos para movimentos de mira rápidos quando a tecla do aimbot é pressionada";
    pt[TextID::FlickDuration] = "Duração em segundos que o Flick permanece ativo após pressionar a tecla, reduzindo suavização/inércia aos valores mínimos (0.1 = muito rápido, 3.0 = longa duração)";
    pt[TextID::FlickReductionType] = "Tipo de curva de redução: Linear (gradual), Exponencial (início rápido, fim suave), Quadrático (início suave, fim rápido)";

    // Tooltips - Bullet TP
    pt[TextID::BulletTP] = "Ativa o sistema de teleporte de projéteis para acertar alvos automaticamente";
    pt[TextID::ShowBulletTPFOVCircle] = "Mostra um círculo na tela que representa a área de atuação do Bullet TP";
    pt[TextID::BulletTPFOVRadius] = "Define o tamanho do raio do círculo de FOV do Bullet TP";
    pt[TextID::BulletTPTarget] = "Seleciona qual parte do inimigo será alvo do Bullet TP";
    pt[TextID::MinSafeDistance] = "Distância mínima para teleporte seguro de projéteis";
    pt[TextID::MaxSafeDistance] = "Distância máxima para ajuste de teleporte de projéteis";
    pt[TextID::ContinueAfterKeyRelease] = "Continuar teleportando projéteis por um curto período após soltar a tecla";
    pt[TextID::ContinueDuration] = "Quanto tempo continuar teleportando projéteis após soltar a tecla";
    pt[TextID::HitPrecision] = "Precisão do teleporte de projéteis (0.98 = 98% da distância até o alvo)";
    pt[TextID::DebugMode] = "Ativar logs de depuração para o sistema BulletTP";

    // Tooltips - Humanization
    pt[TextID::TargetBodyPartMode] = "Seleciona qual parte do corpo mirar (Cabeça, Pescoço, Peito, Coluna, Aleatório ou Humanizado)";
    pt[TextID::HeadProbability] = "Probabilidade de mirar na cabeça quando usar o modo humanizado";
    pt[TextID::NeckProbability] = "Probabilidade de mirar no pescoço quando usar o modo humanizado";
    pt[TextID::ChestProbability] = "Probabilidade de mirar no peito quando usar o modo humanizado";
    pt[TextID::SpineProbability] = "Probabilidade de mirar na coluna quando usar o modo humanizado";
    pt[TextID::BodyPartUpdateInterval] = "Tempo em segundos entre mudanças de parte do corpo no modo humanizado";

    // Tooltips - Keybinds
    pt[TextID::UseGamepad] = "Ativa suporte a gamepad para ativação do aimbot e bullet TP";
    pt[TextID::AimbotKey] = "Define a tecla usada para ativar o aimbot";
    pt[TextID::BulletTPKey] = "Define a tecla usada para ativar o Bullet TP";

    // Tooltips - Checks
    pt[TextID::SelfCheck] = "Evita que as funcionalidades sejam aplicadas a você mesmo";
    pt[TextID::VisibleCheck] = "Verifica se o alvo está visível antes de aplicar as funcionalidades";
    pt[TextID::UseLineOfSight] = "Usa LineOfSight para verificação de visibilidade mais precisa, detectando inimigos atrás de paredes de forma mais confiável";
    pt[TextID::TeamCheck] = "Ignora companheiros de equipe ao usar funcionalidades como Aimbot, ESP e Bullet TP";

    // Tooltips - ESP
    pt[TextID::EnableESP] = "Ativa todos os recursos do ESP (barras de vida, distância, linhas de rastreamento, etc.)";
    pt[TextID::TracerLines] = "Desenha linhas que conectam o centro da tela aos jogadores inimigos";
    pt[TextID::ShowHealth] = "Mostra a quantidade de vida dos jogadores inimigos";
    pt[TextID::ShowDistance] = "Mostra a distância entre você e os jogadores inimigos";
    pt[TextID::ShowUltimatePercentage] = "Mostra a porcentagem de carga da ultimate dos jogadores inimigos";
    pt[TextID::EnableGlow] = "Ativa o efeito de brilho nos jogadores inimigos, permitindo vê-los através das paredes";

    // Tooltips - FOV
    pt[TextID::FOVChanger] = "Altera o campo de visão do jogo para uma visão mais ampla";
    pt[TextID::FOVSlider] = "Define o valor do campo de visão (quanto maior, mais ampla a visão)";

    // Tooltips - Config
    pt[TextID::Language] = "Altera o idioma da interface";
    pt[TextID::SaveConfig] = "Salva a configuração atual em um arquivo";
    pt[TextID::LoadConfig] = "Carrega uma configuração de um arquivo";
    pt[TextID::DeleteConfig] = "Exclui um arquivo de configuração salvo";

    // Section Headers
    pt[TextID::AimbotSettings] = "CONFIGURAÇÕES DO AIMBOT";
    pt[TextID::SmoothingSettings] = "CONFIGURAÇÕES DE SUAVIZAÇÃO";
    pt[TextID::BulletTPSettings] = "CONFIGURAÇÕES DO BULLET TP";
    pt[TextID::AdvancedAimbotSettings] = "CONFIGURAÇÕES AVANÇADAS DO AIMBOT";
    pt[TextID::InertiaSettings] = "CONFIGURAÇÕES DE INÉRCIA";
    pt[TextID::AdaptiveMovementSettings] = "CONFIGURAÇÕES DE MOVIMENTO ADAPTATIVO";
    pt[TextID::HumanizationSettings] = "CONFIGURAÇÕES DE HUMANIZAÇÃO";

    pt[TextID::KeybindSettings] = "CONFIGURAÇÕES DE TECLAS";
    pt[TextID::CheckSettings] = "CONFIGURAÇÕES DE VERIFICAÇÃO";
    pt[TextID::ESPSettings] = "CONFIGURAÇÕES DO ESP";
    pt[TextID::FOVSettings] = "CONFIGURAÇÕES DE FOV";
    pt[TextID::ConfigSettings] = "CONFIGURAÇÕES";
    pt[TextID::AdvancedBulletTPSettings] = "CONFIGURAÇÕES AVANÇADAS DO BULLET TP";
    pt[TextID::AvailableConfigurations] = "CONFIGURAÇÕES DISPONÍVEIS";
    pt[TextID::CurrentConfig] = "Configuração Atual";

    // Menu Tabs
    pt[TextID::TabAimbotBulletTP] = "AIMBOT & BULLET TP";
    pt[TextID::TabVisuals] = "VISUAIS";
    pt[TextID::TabMisc] = "DIVERSOS";
    pt[TextID::TabConfig] = "CONFIGURAÇÃO";

    // Menu Items
    pt[TextID::AimbotLabel] = "Aimbot";
    pt[TextID::SmoothingLabel] = "Suavização";
    pt[TextID::SeparatePitchYawSmoothingLabel] = "Controle Separado Pitch/Yaw";
    pt[TextID::SmoothingPitchLabel] = "Suavização Pitch (Vertical)";
    pt[TextID::SmoothingYawLabel] = "Suavização Yaw (Horizontal)";
    pt[TextID::FocusLowestHealthLabel] = "Focar Menor Vida";
    pt[TextID::ShowAimbotFOVCircleLabel] = "Mostrar Círculo FOV do Aimbot";
    pt[TextID::AimbotFOVRadiusLabel] = "Raio do FOV do Aimbot";

    // Advanced Aimbot Labels
    pt[TextID::AdvancedAimbotSettingsLabel] = "Configurações Avançadas do Aimbot";
    pt[TextID::UseInertiaLabel] = "Usar Inércia";
    pt[TextID::InertiaFactorLabel] = "Fator de Inércia";
    pt[TextID::UseAimDelayLabel] = "Usar Delay de Mira";
    pt[TextID::AimDelayTimeLabel] = "Tempo de Delay (s)";
    pt[TextID::UseAdaptiveMovementLabel] = "Usar Movimento Adaptativo";
    pt[TextID::AdaptiveDurationLabel] = "Duração Adaptativa (s)";
    pt[TextID::InitialSmoothingLabel] = "Suavização Inicial";
    pt[TextID::MinimumSmoothingLabel] = "Suavização Mínima";
    pt[TextID::SeparateAdaptiveSettingsLabel] = "Configurações Adaptativas Separadas Pitch/Yaw";
    pt[TextID::InitialPitchSmoothingLabel] = "Suavização Inicial Pitch";
    pt[TextID::MinimumPitchSmoothingLabel] = "Suavização Mínima Pitch";
    pt[TextID::InitialYawSmoothingLabel] = "Suavização Inicial Yaw";
    pt[TextID::MinimumYawSmoothingLabel] = "Suavização Mínima Yaw";
    pt[TextID::UseAdaptiveInertiaLabel] = "Usar Inércia Adaptativa";
    pt[TextID::InitialInertiaFactorLabel] = "Fator de Inércia Inicial";
    pt[TextID::MinimumInertiaFactorLabel] = "Fator de Inércia Mínimo";

    // Labels do Sistema Flick
    pt[TextID::FlickSettingsLabel] = "CONFIGURAÇÕES DO FLICK";
    pt[TextID::UseFlickLabel] = "Usar Flick";
    pt[TextID::FlickDurationLabel] = "Duração do Flick";
    pt[TextID::FlickReductionTypeLabel] = "Tipo de Redução";

    // Humanization Labels
    pt[TextID::HumanizationSettingsLabel] = "Configurações de Humanização";
    pt[TextID::TargetBodyPartModeLabel] = "Parte do Corpo Alvo";
    pt[TextID::ChestOption] = "Peito";
    pt[TextID::SpineOption] = "Coluna";
    pt[TextID::HumanizedOption] = "Humanizado";
    pt[TextID::HeadProbabilityLabel] = "Probabilidade da Cabeça";
    pt[TextID::NeckProbabilityLabel] = "Probabilidade do Pescoço";
    pt[TextID::ChestProbabilityLabel] = "Probabilidade do Peito";
    pt[TextID::SpineProbabilityLabel] = "Probabilidade da Coluna";
    pt[TextID::BodyPartUpdateIntervalLabel] = "Intervalo de Atualização (s)";

    // Bullet TP Labels
    pt[TextID::BulletTPLabel] = "Bullet TP";
    pt[TextID::ShowBulletTPFOVCircleLabel] = "Mostrar Círculo FOV do Bullet TP";
    pt[TextID::BulletTPFOVRadiusLabel] = "Raio do FOV do Bullet TP";
    pt[TextID::BulletTPTargetLabel] = "Alvo do Bullet TP";
    pt[TextID::HeadOption] = "Cabeça";
    pt[TextID::NeckOption] = "Pescoço";
    pt[TextID::RandomOption] = "Aleatório";
    pt[TextID::AdvancedBulletTPSettingsLabel] = "Configurações Avançadas do Bullet TP";
    pt[TextID::MinSafeDistanceLabel] = "Distância Mínima Segura";
    pt[TextID::MaxSafeDistanceLabel] = "Distância Máxima Segura";
    pt[TextID::ContinueAfterKeyReleaseLabel] = "Continuar Após Soltar a Tecla";
    pt[TextID::ContinueDurationLabel] = "Duração de Continuação (s)";
    pt[TextID::HitPrecisionLabel] = "Precisão de Acerto";
    pt[TextID::DebugModeLabel] = "Modo de Depuração";

    pt[TextID::UseGamepadLabel] = "Usar Gamepad";
    pt[TextID::AimbotKeyLabel] = "Tecla do Aimbot";
    pt[TextID::BulletTPKeyLabel] = "Tecla do Bullet TP";
    pt[TextID::SelfCheckLabel] = "Verificar Jogador Local";
    pt[TextID::VisibleCheckLabel] = "Verificar Visibilidade";
    pt[TextID::UseLineOfSightLabel] = "Usar LineOfSight (Melhor Precisão)";
    pt[TextID::TeamCheckLabel] = "Verificar Equipe";
    pt[TextID::EnableESPLabel] = "Ativar ESP";
    pt[TextID::TracerLinesLabel] = "Linhas de Rastreamento";
    pt[TextID::ShowHealthLabel] = "Vida";
    pt[TextID::ShowDistanceLabel] = "Distância";
    pt[TextID::ShowUltimatePercentageLabel] = "Porcentagem da Ultimate";
    pt[TextID::EnableGlowLabel] = "Ativar Brilho";
    pt[TextID::FOVChangerLabel] = "Alterador de FOV";
    pt[TextID::FOVSliderLabel] = "Controle de FOV";
    pt[TextID::LanguageLabel] = "Idioma";
    pt[TextID::SaveConfigLabel] = "Salvar Configuração";
    pt[TextID::LoadConfigLabel] = "Carregar Configuração";
    pt[TextID::DeleteConfigLabel] = "Excluir Configuração";
    pt[TextID::ConfigNameLabel] = "Nome da Configuração";
    pt[TextID::SaveButtonLabel] = "Salvar";
    pt[TextID::LoadButtonLabel] = "Carregar";
    pt[TextID::DeleteButtonLabel] = "Excluir";

    // Role Target
    pt[TextID::RoleTargetSettings] = "CONFIGURAÇÕES DE ALVO POR FUNÇÃO";
    pt[TextID::EnableRoleTargetLabel] = "Ativar Alvo por Função";
    pt[TextID::TargetRoleLabel] = "Função Alvo";
    pt[TextID::AllRolesOption] = "Todas as Funções";
    pt[TextID::TankOption] = "Tanque";
    pt[TextID::DamageOption] = "Dano";
    pt[TextID::SupportOption] = "Suporte";

    // Tabs
    pt[TextID::KeybindsAndChecksTab] = "TECLAS & VERIFICAÇÕES";

    // App Title
    pt[TextID::AppTitle] = "Rivals UnKnoWn";

    // Config UI
    pt[TextID::CurrentConfigLabel] = "Configuração Atual:";
    pt[TextID::RefreshListButton] = "Atualizar Lista";
    pt[TextID::AvailableConfigurationsLabel] = "Configurações Disponíveis:";
    pt[TextID::SaveButton] = "Salvar";
    pt[TextID::LoadButton] = "Carregar";
    pt[TextID::DeleteButton] = "Excluir";

    // Advanced Settings
    pt[TextID::DelaySettingsLabel] = "CONFIGURAÇÕES DE DELAY";

    // Humanization System
    pt[TextID::EnableHumanizedTargetingLabel] = "Ativar Mira Humanizada";
    pt[TextID::EnableHumanizedTargetingTooltip] = "Ativa ou desativa o sistema de mira humanizada";
    pt[TextID::BodyPartProbabilitiesLabel] = "Probabilidades das Partes do Corpo (0.0-1.0):";
    pt[TextID::ProbabilitiesZeroInfoLabel] = "Info: Todas as probabilidades são zero. Distribuição igual será usada.";
    pt[TextID::ProbabilitiesNormalizedInfoLabel] = "Info: Probabilidades serão normalizadas (Total: %.2f)";
    pt[TextID::DynamicTimingLabel] = "Tempo Dinâmico (segundos):";
    pt[TextID::MinChangeTimeLabel] = "Tempo Mínimo de Mudança";
    pt[TextID::MinChangeTimeTooltip] = "Tempo mínimo entre mudanças de partes do corpo no modo humanizado";
    pt[TextID::MaxChangeTimeLabel] = "Tempo Máximo de Mudança";
    pt[TextID::MaxChangeTimeTooltip] = "Tempo máximo entre mudanças de partes do corpo no modo humanizado";
    pt[TextID::RandomModeInfoLabel] = "Nota: Modo aleatório usa intervalos variáveis (2-4 segundos)";
    pt[TextID::InitialValueFromInertiaLabel] = "Valor inicial: %.1f (do Fator de Inércia)";
    pt[TextID::InitialValueFromSmoothingLabel] = "Valor inicial: %.1f (da Suavização)";
    pt[TextID::InitialValueFromPitchSmoothingLabel] = "Valor inicial: %.1f (da Suavização Pitch)";
    pt[TextID::InitialValueFromYawSmoothingLabel] = "Valor inicial: %.1f (da Suavização Yaw)";
    pt[TextID::AdaptiveSmoothingLabel] = "Suavização Adaptativa";
    pt[TextID::NoneKeyLabel] = "Nenhuma";

    // Role Target Tooltips
    pt[TextID::EnableRoleTargetTooltip] = "Ativa o direcionamento apenas para funções específicas de heróis (Tanque, Dano, Suporte)";
    pt[TextID::TargetRoleTooltip] = "Selecione qual função será alvo do Aimbot e Bullet TP\nSe nenhum alvo da função selecionada for encontrado, recorrerá na ordem: Suporte > Dano > Tanque";

    // Support Settings
    pt[TextID::SupportSettingsLabel] = "CONFIGURAÇÕES DE SUPORTE";

    // Team Target Mode
    pt[TextID::TeamTargetModeLabel] = "Modo de Alvo de Equipe";
    pt[TextID::TeamTargetModeTooltip] = "Quando ativado, mira em aliados em vez de inimigos. Útil para jogadores de suporte";
    pt[TextID::TeamTargetKeyLabel] = "Tecla de Alvo de Equipe";
    pt[TextID::TeamTargetKeyTooltip] = "Tecla para alternar entre mirar em inimigos ou aliados";

    // Support Vision
    pt[TextID::SupportVisionLabel] = "Visão de Suporte (Visão 360°)";
    pt[TextID::SupportVisionTooltip] = "Ignora restrições de FOV, permitindo mirar em aliados/inimigos em qualquer direção";

    // Flying Target Priority
    pt[TextID::FlyingTargetPriorityLabel] = "Priorizar Alvos Voadores";
    pt[TextID::FlyingTargetPriorityTooltip] = "Prioriza alvos que estão no ar (pulando, voando, etc.)";
    pt[TextID::FlyingVelocityThresholdLabel] = "Limiar de Velocidade de Voo";
    pt[TextID::FlyingVelocityThresholdTooltip] = "Velocidade vertical mínima para considerar um alvo como voador";

    // UI Zoom Settings
    pt[TextID::UIZoomSettings] = "CONFIGURAÇÕES DE ZOOM DA INTERFACE";
    pt[TextID::EnableUIZoom] = "Ativa o zoom da interface com CTRL+Roda do Mouse";
    pt[TextID::EnableUIZoomLabel] = "Ativar Zoom da Interface";
    pt[TextID::UIZoomFactor] = "Define o nível de zoom para a interface do usuário";
    pt[TextID::UIZoomFactorLabel] = "Fator de Zoom da Interface";
    pt[TextID::ResetZoomButton] = "Resetar Zoom";

    // Profile Management
    pt[TextID::UpdateCurrentProfileButton] = "Atualizar Perfil Atual";
    pt[TextID::UpdateCurrentProfileTooltip] = "Salva as configurações atuais no perfil ativo";

    // Definir as strings em espanhol
    auto &es = m_strings[Language::Spanish];

    // Tooltips - Aimbot
    es[TextID::Aimbot] = "Activa el sistema de apuntado automático que se bloquea en el objetivo más cercano";
    es[TextID::Smoothing] = "Controla la suavidad del movimiento del aimbot (mayor = más suave)";
    es[TextID::FocusLowestHealth] = "Prioriza objetivos con la salud más baja en lugar de la distancia más cercana";
    es[TextID::ShowAimbotFOVCircle] = "Muestra un círculo en la pantalla que representa el campo de visión del aimbot";
    es[TextID::AimbotFOVRadius] = "Establece el tamaño del radio del círculo FOV del aimbot";

    // Tooltips - Bullet TP
    es[TextID::BulletTP] = "Activa el sistema de teletransporte de balas para golpear objetivos automáticamente";
    es[TextID::ShowBulletTPFOVCircle] = "Muestra un círculo en la pantalla que representa el campo de visión del Bullet TP";
    es[TextID::BulletTPFOVRadius] = "Establece el tamaño del radio del círculo FOV del Bullet TP";
    es[TextID::BulletTPTarget] = "Selecciona qué parte del enemigo será el objetivo del Bullet TP";
    es[TextID::MinSafeDistance] = "Distancia mínima para el teletransporte seguro de balas";
    es[TextID::MaxSafeDistance] = "Distancia máxima para el ajuste de teletransporte de balas";

    // Tooltips - Sistema Flick
    es[TextID::UseFlick] = "Activa el sistema Flick autónomo que reduce suavizado e inercia a valores mínimos para movimientos de puntería rápidos cuando se presiona la tecla del aimbot";
    es[TextID::FlickDuration] = "Duración en segundos que el Flick permanece activo después de presionar la tecla, reduciendo suavizado/inercia a valores mínimos (0.1 = muy rápido, 3.0 = larga duración)";
    es[TextID::FlickReductionType] = "Tipo de curva de reducción: Lineal (gradual), Exponencial (inicio rápido, final suave), Cuadrático (inicio suave, final rápido)";

    // Tooltips - Keybinds
    es[TextID::UseGamepad] = "Activa el soporte para gamepad para la activación del aimbot y bullet TP";
    es[TextID::AimbotKey] = "Establece la tecla utilizada para activar el aimbot";
    es[TextID::BulletTPKey] = "Establece la tecla utilizada para activar el Bullet TP";

    // Tooltips - Checks
    es[TextID::SelfCheck] = "Evita que las funciones se apliquen a ti mismo";
    es[TextID::VisibleCheck] = "Comprueba si el objetivo es visible antes de aplicar las funciones";
    es[TextID::UseLineOfSight] = "Utiliza LineOfSight para una verificación de visibilidad más precisa, detectando enemigos detrás de paredes de forma más fiable";
    es[TextID::TeamCheck] = "Ignora a los compañeros de equipo al usar funciones como Aimbot, ESP y Bullet TP";

    // Tooltips - ESP
    es[TextID::TracerLines] = "Dibuja líneas que conectan el centro de la pantalla con los jugadores enemigos";
    es[TextID::ShowHealth] = "Muestra la cantidad de salud de los jugadores enemigos";
    es[TextID::ShowDistance] = "Muestra la distancia entre tú y los jugadores enemigos";
    es[TextID::ShowUltimatePercentage] = "Muestra el porcentaje de carga de la ultimate de los jugadores enemigos";
    es[TextID::EnableGlow] = "Activa el efecto de brillo en los jugadores enemigos, permitiéndote verlos a través de las paredes";

    // Tooltips - FOV
    es[TextID::FOVChanger] = "Cambia el campo de visión del juego para una perspectiva más amplia";
    es[TextID::FOVSlider] = "Establece el valor del campo de visión (mayor = vista más amplia)";

    // Tooltips - Config
    es[TextID::Language] = "Cambia el idioma de la interfaz";
    es[TextID::SaveConfig] = "Guarda la configuración actual en un archivo";
    es[TextID::LoadConfig] = "Carga una configuración desde un archivo";
    es[TextID::DeleteConfig] = "Elimina un archivo de configuración guardado";

    // Section Headers
    es[TextID::AimbotSettings] = "CONFIGURACIÓN DEL AIMBOT";
    es[TextID::SmoothingSettings] = "CONFIGURACIÓN DE SUAVIZADO";
    es[TextID::BulletTPSettings] = "CONFIGURACIÓN DEL BULLET TP";

    // Role Target Tooltips
    es[TextID::EnableRoleTargetTooltip] = "Activa el apuntado solo a roles específicos de héroes (Tanque, Daño, Apoyo)";
    es[TextID::TargetRoleTooltip] = "Selecciona qué rol será el objetivo del Aimbot y Bullet TP\nSi no se encuentra ningún objetivo del rol seleccionado, recurrirá en orden: Apoyo > Daño > Tanque";

    // Support Settings
    es[TextID::SupportSettingsLabel] = "CONFIGURACIÓN DE APOYO";

    // Team Target Mode
    es[TextID::TeamTargetModeLabel] = "Modo de Objetivo de Equipo";
    es[TextID::TeamTargetModeTooltip] = "Cuando está activado, apunta a aliados en lugar de enemigos. Útil para jugadores de apoyo";
    es[TextID::TeamTargetKeyLabel] = "Tecla de Objetivo de Equipo";
    es[TextID::TeamTargetKeyTooltip] = "Tecla para alternar entre apuntar a enemigos o aliados";

    // Support Vision
    es[TextID::SupportVisionLabel] = "Visión de Apoyo (Vista 360°)";
    es[TextID::SupportVisionTooltip] = "Ignora las restricciones de FOV, permitiendo apuntar a aliados/enemigos en cualquier dirección";

    // Flying Target Priority
    es[TextID::FlyingTargetPriorityLabel] = "Priorizar Objetivos Voladores";
    es[TextID::FlyingTargetPriorityTooltip] = "Prioriza objetivos que están en el aire (saltando, volando, etc.)";
    es[TextID::FlyingVelocityThresholdLabel] = "Umbral de Velocidad de Vuelo";
    es[TextID::FlyingVelocityThresholdTooltip] = "Velocidad vertical mínima para considerar un objetivo como volador";

    // UI Zoom Settings
    es[TextID::UIZoomSettings] = "CONFIGURACIÓN DE ZOOM DE INTERFAZ";
    es[TextID::EnableUIZoom] = "Activa el zoom de la interfaz con CTRL+Rueda del Ratón";
    es[TextID::EnableUIZoomLabel] = "Activar Zoom de Interfaz";
    es[TextID::UIZoomFactor] = "Establece el nivel de zoom para la interfaz de usuario";
    es[TextID::UIZoomFactorLabel] = "Factor de Zoom de Interfaz";
    es[TextID::ResetZoomButton] = "Restablecer Zoom";

    // Profile Management
    es[TextID::UpdateCurrentProfileButton] = "Actualizar Perfil Actual";
    es[TextID::UpdateCurrentProfileTooltip] = "Guarda la configuración actual en el perfil activo";

    es[TextID::KeybindSettings] = "CONFIGURACIÓN DE TECLAS";
    es[TextID::CheckSettings] = "CONFIGURACIÓN DE VERIFICACIÓN";
    es[TextID::ESPSettings] = "CONFIGURACIÓN DEL ESP";
    es[TextID::FOVSettings] = "CONFIGURACIÓN DE FOV";
    es[TextID::ConfigSettings] = "CONFIGURACIÓN";
    es[TextID::AdvancedBulletTPSettings] = "CONFIGURACIÓN AVANZADA DEL BULLET TP";
    es[TextID::AvailableConfigurations] = "CONFIGURACIONES DISPONIBLES";
    es[TextID::CurrentConfig] = "Configuración Actual";

    // Menu Tabs
    es[TextID::TabAimbotBulletTP] = "AIMBOT & BULLET TP";
    es[TextID::TabVisuals] = "VISUALES";
    es[TextID::TabMisc] = "MISC";
    es[TextID::TabConfig] = "CONFIGURACIÓN";

    // Menu Items
    es[TextID::AimbotLabel] = "Aimbot";
    es[TextID::SmoothingLabel] = "Suavizado";
    es[TextID::FocusLowestHealthLabel] = "Enfocar Menor Salud";
    es[TextID::ShowAimbotFOVCircleLabel] = "Mostrar Círculo FOV del Aimbot";
    es[TextID::AimbotFOVRadiusLabel] = "Radio FOV del Aimbot";
    es[TextID::BulletTPLabel] = "Bullet TP";
    es[TextID::ShowBulletTPFOVCircleLabel] = "Mostrar Círculo FOV del Bullet TP";
    es[TextID::BulletTPFOVRadiusLabel] = "Radio FOV del Bullet TP";
    es[TextID::BulletTPTargetLabel] = "Objetivo del Bullet TP";
    es[TextID::HeadOption] = "Cabeza";
    es[TextID::NeckOption] = "Cuello";
    es[TextID::RandomOption] = "Aleatorio";
    es[TextID::AdvancedBulletTPSettingsLabel] = "Configuración Avanzada del Bullet TP";
    es[TextID::MinSafeDistanceLabel] = "Distancia Mínima Segura";
    es[TextID::MaxSafeDistanceLabel] = "Distancia Máxima Segura";

    // Labels del Sistema Flick
    es[TextID::FlickSettingsLabel] = "CONFIGURACIÓN DEL FLICK";
    es[TextID::UseFlickLabel] = "Usar Flick";
    es[TextID::FlickDurationLabel] = "Duración del Flick";
    es[TextID::FlickReductionTypeLabel] = "Tipo de Reducción";

    es[TextID::UseGamepadLabel] = "Usar Gamepad";
    es[TextID::AimbotKeyLabel] = "Tecla de Aimbot";
    es[TextID::BulletTPKeyLabel] = "Tecla de Bullet TP";
    es[TextID::SelfCheckLabel] = "Verificación Propia";
    es[TextID::VisibleCheckLabel] = "Verificación de Visibilidad";
    es[TextID::UseLineOfSightLabel] = "Usar LineOfSight (Mejor Precisión)";
    es[TextID::TeamCheckLabel] = "Verificación de Equipo";
    es[TextID::TracerLinesLabel] = "Líneas de Rastreo";
    es[TextID::ShowHealthLabel] = "Salud";
    es[TextID::ShowDistanceLabel] = "Distancia";
    es[TextID::ShowUltimatePercentageLabel] = "Porcentaje de Ultimate";
    es[TextID::EnableGlowLabel] = "Activar Brillo";
    es[TextID::FOVChangerLabel] = "Cambiador de FOV";
    es[TextID::FOVSliderLabel] = "Control de FOV";
    es[TextID::LanguageLabel] = "Idioma";
    es[TextID::SaveConfigLabel] = "Guardar Configuración";
    es[TextID::LoadConfigLabel] = "Cargar Configuración";
    es[TextID::DeleteConfigLabel] = "Eliminar Configuración";
    es[TextID::ConfigNameLabel] = "Nombre de Configuración";
    es[TextID::SaveButtonLabel] = "Guardar";
    es[TextID::LoadButtonLabel] = "Cargar";
    es[TextID::DeleteButtonLabel] = "Eliminar";

    // Role Target
    es[TextID::RoleTargetSettings] = "CONFIGURACIÓN DE OBJETIVO POR ROL";
    es[TextID::EnableRoleTargetLabel] = "Activar Objetivo por Rol";
    es[TextID::TargetRoleLabel] = "Rol Objetivo";
    es[TextID::AllRolesOption] = "Todos los Roles";
    es[TextID::TankOption] = "Tanque";
    es[TextID::DamageOption] = "Daño";
    es[TextID::SupportOption] = "Apoyo";

    // Tabs
    es[TextID::KeybindsAndChecksTab] = "TECLAS & VERIFICACIONES";

    // App Title
    es[TextID::AppTitle] = "Rivals UnKnoWn";

    // Config UI
    es[TextID::CurrentConfigLabel] = "Configuración Actual:";
    es[TextID::RefreshListButton] = "Actualizar Lista";
    es[TextID::AvailableConfigurationsLabel] = "Configuraciones Disponibles:";
    es[TextID::SaveButton] = "Guardar";
    es[TextID::LoadButton] = "Cargar";
    es[TextID::DeleteButton] = "Eliminar";

    // Advanced Settings
    es[TextID::DelaySettingsLabel] = "CONFIGURACIÓN DE RETRASO";

    // Humanization System
    es[TextID::EnableHumanizedTargetingLabel] = "Activar Puntería Humanizada";
    es[TextID::EnableHumanizedTargetingTooltip] = "Activa o desactiva el sistema de puntería humanizada";
    es[TextID::BodyPartProbabilitiesLabel] = "Probabilidades de Partes del Cuerpo (0.0-1.0):";
    es[TextID::ProbabilitiesZeroInfoLabel] = "Info: Todas las probabilidades son cero. Se usará distribución igual.";
    es[TextID::ProbabilitiesNormalizedInfoLabel] = "Info: Las probabilidades serán normalizadas (Total: %.2f)";
    es[TextID::DynamicTimingLabel] = "Tiempo Dinámico (segundos):";
    es[TextID::MinChangeTimeLabel] = "Tiempo Mínimo de Cambio";
    es[TextID::MinChangeTimeTooltip] = "Tiempo mínimo entre cambios de partes del cuerpo en modo humanizado";
    es[TextID::MaxChangeTimeLabel] = "Tiempo Máximo de Cambio";
    es[TextID::MaxChangeTimeTooltip] = "Tiempo máximo entre cambios de partes del cuerpo en modo humanizado";
    es[TextID::RandomModeInfoLabel] = "Nota: El modo aleatorio usa intervalos variables (2-4 segundos)";
    es[TextID::InitialValueFromInertiaLabel] = "Valor inicial: %.1f (del Factor de Inercia)";
    es[TextID::InitialValueFromSmoothingLabel] = "Valor inicial: %.1f (del Suavizado)";
    es[TextID::InitialValueFromPitchSmoothingLabel] = "Valor inicial: %.1f (del Suavizado Pitch)";
    es[TextID::InitialValueFromYawSmoothingLabel] = "Valor inicial: %.1f (del Suavizado Yaw)";
    es[TextID::AdaptiveSmoothingLabel] = "Suavizado Adaptativo";
    es[TextID::NoneKeyLabel] = "Ninguna";

    // Role Target Tooltips
    es[TextID::EnableRoleTargetTooltip] = "Activa el enfoque solo en roles específicos de héroes (Tanque, Daño, Apoyo)";
    es[TextID::TargetRoleTooltip] = "Selecciona qué rol será el objetivo del Aimbot y Bullet TP\nSi no se encuentra ningún objetivo del rol seleccionado, recurrirá en orden: Apoyo > Daño > Tanque";

    // Definir as strings em francês
    auto &fr = m_strings[Language::French];

    // Tooltips - Aimbot
    fr[TextID::Aimbot] = "Active le système de visée automatique qui se verrouille sur la cible la plus proche";
    fr[TextID::Smoothing] = "Contrôle la fluidité du mouvement de l'aimbot (plus élevé = plus fluide)";
    fr[TextID::FocusLowestHealth] = "Priorise les cibles avec la santé la plus basse au lieu de la distance la plus proche";
    fr[TextID::ShowAimbotFOVCircle] = "Affiche un cercle à l'écran qui représente le champ de vision de l'aimbot";
    fr[TextID::AimbotFOVRadius] = "Définit la taille du rayon du cercle FOV de l'aimbot";

    // Tooltips - Bullet TP
    fr[TextID::BulletTP] = "Active le système de téléportation de balles pour toucher automatiquement les cibles";
    fr[TextID::ShowBulletTPFOVCircle] = "Affiche un cercle à l'écran qui représente le champ de vision du Bullet TP";
    fr[TextID::BulletTPFOVRadius] = "Définit la taille du rayon du cercle FOV du Bullet TP";
    fr[TextID::BulletTPTarget] = "Sélectionne quelle partie de l'ennemi sera ciblée par le Bullet TP";
    fr[TextID::MinSafeDistance] = "Distance minimale pour la téléportation sécurisée des balles";
    fr[TextID::MaxSafeDistance] = "Distance maximale pour l'ajustement de la téléportation des balles";

    // Tooltips - Keybinds
    fr[TextID::UseGamepad] = "Active la prise en charge de la manette pour l'activation de l'aimbot et du bullet TP";
    fr[TextID::AimbotKey] = "Définit la touche utilisée pour activer l'aimbot";
    fr[TextID::BulletTPKey] = "Définit la touche utilisée pour activer le Bullet TP";

    // Tooltips - Checks
    fr[TextID::SelfCheck] = "Empêche que les fonctionnalités soient appliquées à vous-même";
    fr[TextID::VisibleCheck] = "Vérifie si la cible est visible avant d'appliquer les fonctionnalités";
    fr[TextID::UseLineOfSight] = "Utilise LineOfSight pour une vérification de visibilité plus précise, détectant les ennemis derrière les murs de manière plus fiable";
    fr[TextID::TeamCheck] = "Ignore les coéquipiers lors de l'utilisation de fonctionnalités comme Aimbot, ESP et Bullet TP";

    // Tooltips - ESP
    fr[TextID::TracerLines] = "Dessine des lignes reliant le centre de l'écran aux joueurs ennemis";
    fr[TextID::ShowHealth] = "Affiche la quantité de santé des joueurs ennemis";
    fr[TextID::ShowDistance] = "Affiche la distance entre vous et les joueurs ennemis";
    fr[TextID::ShowUltimatePercentage] = "Affiche le pourcentage de charge de l'ultimate des joueurs ennemis";
    fr[TextID::EnableGlow] = "Active l'effet de brillance sur les joueurs ennemis, vous permettant de les voir à travers les murs";

    // Tooltips - FOV
    fr[TextID::FOVChanger] = "Change le champ de vision du jeu pour une perspective plus large";
    fr[TextID::FOVSlider] = "Définit la valeur du champ de vision (plus élevé = vue plus large)";

    // Tooltips - Config
    fr[TextID::Language] = "Change la langue de l'interface";
    fr[TextID::SaveConfig] = "Enregistre la configuration actuelle dans un fichier";
    fr[TextID::LoadConfig] = "Charge une configuration à partir d'un fichier";
    fr[TextID::DeleteConfig] = "Supprime un fichier de configuration enregistré";

    // Section Headers
    fr[TextID::AimbotSettings] = "PARAMÈTRES DE L'AIMBOT";
    fr[TextID::SmoothingSettings] = "PARAMÈTRES DE LISSAGE";
    fr[TextID::BulletTPSettings] = "PARAMÈTRES DU BULLET TP";

    fr[TextID::KeybindSettings] = "PARAMÈTRES DES TOUCHES";
    fr[TextID::CheckSettings] = "PARAMÈTRES DE VÉRIFICATION";
    fr[TextID::ESPSettings] = "PARAMÈTRES ESP";
    fr[TextID::FOVSettings] = "PARAMÈTRES FOV";
    fr[TextID::ConfigSettings] = "PARAMÈTRES";
    fr[TextID::AdvancedBulletTPSettings] = "PARAMÈTRES AVANCÉS DU BULLET TP";
    fr[TextID::AvailableConfigurations] = "CONFIGURATIONS DISPONIBLES";
    fr[TextID::CurrentConfig] = "Configuration Actuelle";

    // Menu Tabs
    fr[TextID::TabAimbotBulletTP] = "AIMBOT & BULLET TP";
    fr[TextID::TabVisuals] = "VISUELS";
    fr[TextID::TabMisc] = "DIVERS";
    fr[TextID::TabConfig] = "CONFIGURATION";

    // Menu Items
    fr[TextID::AimbotLabel] = "Aimbot";
    fr[TextID::SmoothingLabel] = "Lissage";
    fr[TextID::FocusLowestHealthLabel] = "Cibler la Santé la Plus Basse";
    fr[TextID::ShowAimbotFOVCircleLabel] = "Afficher le Cercle FOV de l'Aimbot";
    fr[TextID::AimbotFOVRadiusLabel] = "Rayon FOV de l'Aimbot";
    fr[TextID::BulletTPLabel] = "Bullet TP";
    fr[TextID::ShowBulletTPFOVCircleLabel] = "Afficher le Cercle FOV du Bullet TP";
    fr[TextID::BulletTPFOVRadiusLabel] = "Rayon FOV du Bullet TP";
    fr[TextID::BulletTPTargetLabel] = "Cible du Bullet TP";
    fr[TextID::HeadOption] = "Tête";
    fr[TextID::NeckOption] = "Cou";
    fr[TextID::RandomOption] = "Aléatoire";
    fr[TextID::AdvancedBulletTPSettingsLabel] = "Paramètres Avancés du Bullet TP";
    fr[TextID::MinSafeDistanceLabel] = "Distance Minimale Sécurisée";
    fr[TextID::MaxSafeDistanceLabel] = "Distance Maximale Sécurisée";

    fr[TextID::UseGamepadLabel] = "Utiliser la Manette";
    fr[TextID::AimbotKeyLabel] = "Touche d'Aimbot";
    fr[TextID::BulletTPKeyLabel] = "Touche de Bullet TP";
    fr[TextID::SelfCheckLabel] = "Vérification Personnelle";
    fr[TextID::VisibleCheckLabel] = "Vérification de Visibilité";
    fr[TextID::UseLineOfSightLabel] = "Utiliser LineOfSight (Meilleure Précision)";
    fr[TextID::TeamCheckLabel] = "Vérification d'Équipe";
    fr[TextID::TracerLinesLabel] = "Lignes de Traçage";
    fr[TextID::ShowHealthLabel] = "Santé";
    fr[TextID::ShowDistanceLabel] = "Distance";
    fr[TextID::ShowUltimatePercentageLabel] = "Pourcentage d'Ultimate";
    fr[TextID::EnableGlowLabel] = "Activer la Brillance";
    fr[TextID::FOVChangerLabel] = "Modificateur de FOV";
    fr[TextID::FOVSliderLabel] = "Curseur de FOV";
    fr[TextID::LanguageLabel] = "Langue";
    fr[TextID::SaveConfigLabel] = "Sauvegarder la Configuration";
    fr[TextID::LoadConfigLabel] = "Charger la Configuration";
    fr[TextID::DeleteConfigLabel] = "Supprimer la Configuration";
    fr[TextID::ConfigNameLabel] = "Nom de la Configuration";
    fr[TextID::SaveButtonLabel] = "Sauvegarder";
    fr[TextID::LoadButtonLabel] = "Charger";
    fr[TextID::DeleteButtonLabel] = "Supprimer";

    // Role Target
    fr[TextID::RoleTargetSettings] = "PARAMÈTRES DE CIBLAGE PAR RÔLE";
    fr[TextID::EnableRoleTargetLabel] = "Activer le Ciblage par Rôle";
    fr[TextID::TargetRoleLabel] = "Rôle Cible";
    fr[TextID::AllRolesOption] = "Tous les Rôles";
    fr[TextID::TankOption] = "Tank";
    fr[TextID::DamageOption] = "Dégâts";
    fr[TextID::SupportOption] = "Soutien";

    // Tabs
    fr[TextID::KeybindsAndChecksTab] = "TOUCHES & VÉRIFICATIONS";

    // App Title
    fr[TextID::AppTitle] = "Rivals UnKnoWn";

    // Config UI
    fr[TextID::CurrentConfigLabel] = "Configuration Actuelle:";
    fr[TextID::RefreshListButton] = "Actualiser la Liste";
    fr[TextID::AvailableConfigurationsLabel] = "Configurations Disponibles:";
    fr[TextID::SaveButton] = "Sauvegarder";
    fr[TextID::LoadButton] = "Charger";
    fr[TextID::DeleteButton] = "Supprimer";

    // Advanced Settings
    fr[TextID::DelaySettingsLabel] = "PARAMÈTRES DE DÉLAI";

    // Humanization System
    fr[TextID::EnableHumanizedTargetingLabel] = "Activer la Visée Humanisée";
    fr[TextID::EnableHumanizedTargetingTooltip] = "Active ou désactive le système de visée humanisée";
    fr[TextID::BodyPartProbabilitiesLabel] = "Probabilités des Parties du Corps (0.0-1.0):";
    fr[TextID::ProbabilitiesZeroInfoLabel] = "Info: Toutes les probabilités sont nulles. Distribution égale sera utilisée.";
    fr[TextID::ProbabilitiesNormalizedInfoLabel] = "Info: Les probabilités seront normalisées (Total: %.2f)";
    fr[TextID::DynamicTimingLabel] = "Timing Dynamique (secondes):";
    fr[TextID::MinChangeTimeLabel] = "Temps Minimum de Changement";
    fr[TextID::MinChangeTimeTooltip] = "Temps minimum entre les changements de parties du corps en mode humanisé";
    fr[TextID::MaxChangeTimeLabel] = "Temps Maximum de Changement";
    fr[TextID::MaxChangeTimeTooltip] = "Temps maximum entre les changements de parties du corps en mode humanisé";
    fr[TextID::RandomModeInfoLabel] = "Note: Le mode aléatoire utilise des intervalles variables (2-4 secondes)";
    fr[TextID::InitialValueFromInertiaLabel] = "Valeur initiale: %.1f (du Facteur d'Inertie)";
    fr[TextID::InitialValueFromSmoothingLabel] = "Valeur initiale: %.1f (du Lissage)";
    fr[TextID::InitialValueFromPitchSmoothingLabel] = "Valeur initiale: %.1f (du Lissage Pitch)";
    fr[TextID::InitialValueFromYawSmoothingLabel] = "Valeur initiale: %.1f (du Lissage Yaw)";
    fr[TextID::AdaptiveSmoothingLabel] = "Lissage Adaptatif";
    fr[TextID::NoneKeyLabel] = "Aucune";

    // Role Target Tooltips
    fr[TextID::EnableRoleTargetTooltip] = "Active le ciblage uniquement sur des rôles spécifiques de héros (Tank, Dégâts, Soutien)";
    fr[TextID::TargetRoleTooltip] = "Sélectionnez quel rôle cibler avec l'Aimbot et le Bullet TP\nSi aucune cible du rôle sélectionné n'est trouvée, se rabattra dans l'ordre: Soutien > Dégâts > Tank";

    // Support Settings
    fr[TextID::SupportSettingsLabel] = "PARAMÈTRES DE SOUTIEN";

    // Team Target Mode
    fr[TextID::TeamTargetModeLabel] = "Mode Cible d'Équipe";
    fr[TextID::TeamTargetModeTooltip] = "Lorsqu'il est activé, cible les alliés au lieu des ennemis. Utile pour les joueurs de soutien";
    fr[TextID::TeamTargetKeyLabel] = "Touche de Cible d'Équipe";
    fr[TextID::TeamTargetKeyTooltip] = "Touche pour basculer entre cibler les ennemis ou les alliés";

    // Support Vision
    fr[TextID::SupportVisionLabel] = "Vision de Soutien (Vue à 360°)";
    fr[TextID::SupportVisionTooltip] = "Ignore les restrictions de FOV, permettant de cibler les alliés/ennemis dans n'importe quelle direction";

    // Flying Target Priority
    fr[TextID::FlyingTargetPriorityLabel] = "Prioriser les Cibles en Vol";
    fr[TextID::FlyingTargetPriorityTooltip] = "Priorise les cibles qui sont en l'air (sautant, volant, etc.)";
    fr[TextID::FlyingVelocityThresholdLabel] = "Seuil de Vitesse de Vol";
    fr[TextID::FlyingVelocityThresholdTooltip] = "Vitesse verticale minimale pour considérer une cible comme volante";

    // UI Zoom Settings
    fr[TextID::UIZoomSettings] = "PARAMÈTRES DE ZOOM D'INTERFACE";
    fr[TextID::EnableUIZoom] = "Active le zoom de l'interface avec CTRL+Molette de la Souris";
    fr[TextID::EnableUIZoomLabel] = "Activer le Zoom d'Interface";
    fr[TextID::UIZoomFactor] = "Définit le niveau de zoom pour l'interface utilisateur";
    fr[TextID::UIZoomFactorLabel] = "Facteur de Zoom d'Interface";
    fr[TextID::ResetZoomButton] = "Réinitialiser le Zoom";

    // Profile Management
    fr[TextID::UpdateCurrentProfileButton] = "Mettre à Jour le Profil Actuel";
    fr[TextID::UpdateCurrentProfileTooltip] = "Sauvegarde les paramètres actuels dans le profil actif";

    // Definir as strings em italiano
    auto &it = m_strings[Language::Italian];

    // Tooltips - Aimbot
    it[TextID::Aimbot] = "Attiva il sistema di mira automatica che si blocca sul bersaglio più vicino";
    it[TextID::Smoothing] = "Controlla quanto è fluido il movimento dell'aimbot (più alto = più fluido)";
    it[TextID::FocusLowestHealth] = "Dà priorità ai bersagli con la salute più bassa invece che alla distanza più vicina";
    it[TextID::ShowAimbotFOVCircle] = "Mostra un cerchio sullo schermo che rappresenta il campo visivo dell'aimbot";
    it[TextID::AimbotFOVRadius] = "Imposta la dimensione del raggio del cerchio FOV dell'aimbot";

    // Tooltips - Bullet TP
    it[TextID::BulletTP] = "Attiva il sistema di teletrasporto dei proiettili per colpire automaticamente i bersagli";
    it[TextID::ShowBulletTPFOVCircle] = "Mostra un cerchio sullo schermo che rappresenta il campo visivo del Bullet TP";
    it[TextID::BulletTPFOVRadius] = "Imposta la dimensione del raggio del cerchio FOV del Bullet TP";
    it[TextID::BulletTPTarget] = "Seleziona quale parte del nemico sarà il bersaglio del Bullet TP";
    it[TextID::MinSafeDistance] = "Distanza minima per il teletrasporto sicuro dei proiettili";
    it[TextID::MaxSafeDistance] = "Distanza massima per la regolazione del teletrasporto dei proiettili";

    // Tooltips - Keybinds
    it[TextID::UseGamepad] = "Attiva il supporto per gamepad per l'attivazione dell'aimbot e del bullet TP";
    it[TextID::AimbotKey] = "Imposta il tasto utilizzato per attivare l'aimbot";
    it[TextID::BulletTPKey] = "Imposta il tasto utilizzato per attivare il Bullet TP";

    // Tooltips - Checks
    it[TextID::SelfCheck] = "Impedisce che le funzionalità vengano applicate a te stesso";
    it[TextID::VisibleCheck] = "Controlla se il bersaglio è visibile prima di applicare le funzionalità";
    it[TextID::UseLineOfSight] = "Utilizza LineOfSight per un controllo di visibilità più preciso, rilevando i nemici dietro i muri in modo più affidabile";
    it[TextID::TeamCheck] = "Ignora i compagni di squadra quando si utilizzano funzionalità come Aimbot, ESP e Bullet TP";

    // Tooltips - ESP
    it[TextID::TracerLines] = "Disegna linee che collegano il centro dello schermo ai giocatori nemici";
    it[TextID::ShowHealth] = "Mostra la quantità di salute dei giocatori nemici";
    it[TextID::ShowDistance] = "Mostra la distanza tra te e i giocatori nemici";
    it[TextID::ShowUltimatePercentage] = "Mostra la percentuale di carica dell'ultimate dei giocatori nemici";
    it[TextID::EnableGlow] = "Attiva l'effetto di bagliore sui giocatori nemici, permettendoti di vederli attraverso i muri";

    // Tooltips - FOV
    it[TextID::FOVChanger] = "Cambia il campo visivo del gioco per una prospettiva più ampia";
    it[TextID::FOVSlider] = "Imposta il valore del campo visivo (più alto = vista più ampia)";

    // Tooltips - Config
    it[TextID::Language] = "Cambia la lingua dell'interfaccia";
    it[TextID::SaveConfig] = "Salva la configurazione attuale in un file";
    it[TextID::LoadConfig] = "Carica una configurazione da un file";
    it[TextID::DeleteConfig] = "Elimina un file di configurazione salvato";

    // Section Headers
    it[TextID::AimbotSettings] = "IMPOSTAZIONI AIMBOT";
    it[TextID::SmoothingSettings] = "IMPOSTAZIONI SMOOTHING";
    it[TextID::BulletTPSettings] = "IMPOSTAZIONI BULLET TP";

    it[TextID::KeybindSettings] = "IMPOSTAZIONI TASTI";
    it[TextID::CheckSettings] = "IMPOSTAZIONI CONTROLLI";
    it[TextID::ESPSettings] = "IMPOSTAZIONI ESP";
    it[TextID::FOVSettings] = "IMPOSTAZIONI FOV";
    it[TextID::ConfigSettings] = "IMPOSTAZIONI";
    it[TextID::AdvancedBulletTPSettings] = "IMPOSTAZIONI AVANZATE BULLET TP";
    it[TextID::AvailableConfigurations] = "CONFIGURAZIONI DISPONIBILI";
    it[TextID::CurrentConfig] = "Configurazione Attuale";

    // Menu Tabs
    it[TextID::TabAimbotBulletTP] = "AIMBOT & BULLET TP";
    it[TextID::TabVisuals] = "VISUALI";
    it[TextID::TabMisc] = "VARIE";
    it[TextID::TabConfig] = "CONFIGURAZIONE";

    // Menu Items
    it[TextID::AimbotLabel] = "Aimbot";
    it[TextID::SmoothingLabel] = "Smoothing";
    it[TextID::FocusLowestHealthLabel] = "Concentrati sulla Salute Più Bassa";
    it[TextID::ShowAimbotFOVCircleLabel] = "Mostra Cerchio FOV dell'Aimbot";
    it[TextID::AimbotFOVRadiusLabel] = "Raggio FOV dell'Aimbot";
    it[TextID::BulletTPLabel] = "Bullet TP";
    it[TextID::ShowBulletTPFOVCircleLabel] = "Mostra Cerchio FOV del Bullet TP";
    it[TextID::BulletTPFOVRadiusLabel] = "Raggio FOV del Bullet TP";
    it[TextID::BulletTPTargetLabel] = "Bersaglio del Bullet TP";
    it[TextID::HeadOption] = "Testa";
    it[TextID::NeckOption] = "Collo";
    it[TextID::RandomOption] = "Casuale";
    it[TextID::AdvancedBulletTPSettingsLabel] = "Impostazioni Avanzate del Bullet TP";
    it[TextID::MinSafeDistanceLabel] = "Distanza Minima Sicura";
    it[TextID::MaxSafeDistanceLabel] = "Distanza Massima Sicura";

    it[TextID::UseGamepadLabel] = "Usa Gamepad";
    it[TextID::AimbotKeyLabel] = "Tasto Aimbot";
    it[TextID::BulletTPKeyLabel] = "Tasto Bullet TP";
    it[TextID::SelfCheckLabel] = "Controllo Personale";
    it[TextID::VisibleCheckLabel] = "Controllo Visibilità";
    it[TextID::UseLineOfSightLabel] = "Usa LineOfSight (Precisione Migliore)";
    it[TextID::TeamCheckLabel] = "Controllo Squadra";
    it[TextID::TracerLinesLabel] = "Linee Traccianti";
    it[TextID::ShowHealthLabel] = "Salute";
    it[TextID::ShowDistanceLabel] = "Distanza";
    it[TextID::ShowUltimatePercentageLabel] = "Percentuale Ultimate";
    it[TextID::EnableGlowLabel] = "Attiva Bagliore";
    it[TextID::FOVChangerLabel] = "Modificatore FOV";
    it[TextID::FOVSliderLabel] = "Slider FOV";
    it[TextID::LanguageLabel] = "Lingua";
    it[TextID::SaveConfigLabel] = "Salva Configurazione";
    it[TextID::LoadConfigLabel] = "Carica Configurazione";
    it[TextID::DeleteConfigLabel] = "Elimina Configurazione";
    it[TextID::ConfigNameLabel] = "Nome Configurazione";
    it[TextID::SaveButtonLabel] = "Salva";
    it[TextID::LoadButtonLabel] = "Carica";
    it[TextID::DeleteButtonLabel] = "Elimina";

    // Role Target
    it[TextID::RoleTargetSettings] = "IMPOSTAZIONI OBIETTIVO PER RUOLO";
    it[TextID::EnableRoleTargetLabel] = "Attiva Obiettivo per Ruolo";
    it[TextID::TargetRoleLabel] = "Ruolo Obiettivo";
    it[TextID::AllRolesOption] = "Tutti i Ruoli";
    it[TextID::TankOption] = "Tank";
    it[TextID::DamageOption] = "Danno";
    it[TextID::SupportOption] = "Supporto";

    // Tabs
    it[TextID::KeybindsAndChecksTab] = "TASTI & CONTROLLI";

    // App Title
    it[TextID::AppTitle] = "Rivals UnKnoWn";

    // Config UI
    it[TextID::CurrentConfigLabel] = "Configurazione Attuale:";
    it[TextID::RefreshListButton] = "Aggiorna Lista";
    it[TextID::AvailableConfigurationsLabel] = "Configurazioni Disponibili:";
    it[TextID::SaveButton] = "Salva";
    it[TextID::LoadButton] = "Carica";
    it[TextID::DeleteButton] = "Elimina";

    // Advanced Settings
    it[TextID::DelaySettingsLabel] = "IMPOSTAZIONI DI RITARDO";

    // Humanization System
    it[TextID::EnableHumanizedTargetingLabel] = "Attiva Mira Umanizzata";
    it[TextID::EnableHumanizedTargetingTooltip] = "Attiva o disattiva il sistema di mira umanizzata";
    it[TextID::BodyPartProbabilitiesLabel] = "Probabilità Parti del Corpo (0.0-1.0):";
    it[TextID::ProbabilitiesZeroInfoLabel] = "Info: Tutte le probabilità sono zero. Verrà utilizzata distribuzione uguale.";
    it[TextID::ProbabilitiesNormalizedInfoLabel] = "Info: Le probabilità saranno normalizzate (Totale: %.2f)";
    it[TextID::DynamicTimingLabel] = "Tempo Dinamico (secondi):";
    it[TextID::MinChangeTimeLabel] = "Tempo Minimo di Cambio";
    it[TextID::MinChangeTimeTooltip] = "Tempo minimo tra i cambi di parti del corpo in modalità umanizzata";
    it[TextID::MaxChangeTimeLabel] = "Tempo Massimo di Cambio";
    it[TextID::MaxChangeTimeTooltip] = "Tempo massimo tra i cambi di parti del corpo in modalità umanizzata";
    it[TextID::RandomModeInfoLabel] = "Nota: La modalità casuale usa intervalli variabili (2-4 secondi)";
    it[TextID::InitialValueFromInertiaLabel] = "Valore iniziale: %.1f (dal Fattore di Inerzia)";
    it[TextID::InitialValueFromSmoothingLabel] = "Valore iniziale: %.1f (dallo Smoothing)";
    it[TextID::InitialValueFromPitchSmoothingLabel] = "Valore iniziale: %.1f (dallo Smoothing Pitch)";
    it[TextID::InitialValueFromYawSmoothingLabel] = "Valore iniziale: %.1f (dallo Smoothing Yaw)";
    it[TextID::AdaptiveSmoothingLabel] = "Smoothing Adattivo";
    it[TextID::NoneKeyLabel] = "Nessuno";

    // Role Target Tooltips
    it[TextID::EnableRoleTargetTooltip] = "Attiva il targeting solo su ruoli specifici degli eroi (Tank, Danno, Supporto)";
    it[TextID::TargetRoleTooltip] = "Seleziona quale ruolo sarà l'obiettivo dell'Aimbot e del Bullet TP\nSe non viene trovato alcun obiettivo del ruolo selezionato, ricorrerà nell'ordine: Supporto > Danno > Tank";

    // Support Settings
    it[TextID::SupportSettingsLabel] = "IMPOSTAZIONI DI SUPPORTO";

    // Team Target Mode
    it[TextID::TeamTargetModeLabel] = "Modalità Bersaglio Squadra";
    it[TextID::TeamTargetModeTooltip] = "Quando attivato, prende di mira gli alleati invece dei nemici. Utile per i giocatori di supporto";
    it[TextID::TeamTargetKeyLabel] = "Tasto Bersaglio Squadra";
    it[TextID::TeamTargetKeyTooltip] = "Tasto per alternare tra prendere di mira nemici o alleati";

    // Support Vision
    it[TextID::SupportVisionLabel] = "Visione di Supporto (Vista a 360°)";
    it[TextID::SupportVisionTooltip] = "Ignora le restrizioni del FOV, permettendo di prendere di mira alleati/nemici in qualsiasi direzione";

    // Flying Target Priority
    it[TextID::FlyingTargetPriorityLabel] = "Priorità Bersagli in Volo";
    it[TextID::FlyingTargetPriorityTooltip] = "Dà priorità ai bersagli che sono in aria (saltando, volando, ecc.)";
    it[TextID::FlyingVelocityThresholdLabel] = "Soglia di Velocità di Volo";
    it[TextID::FlyingVelocityThresholdTooltip] = "Velocità verticale minima per considerare un bersaglio come in volo";

    // UI Zoom Settings
    it[TextID::UIZoomSettings] = "IMPOSTAZIONI ZOOM INTERFACCIA";
    it[TextID::EnableUIZoom] = "Attiva lo zoom dell'interfaccia con CTRL+Rotella del Mouse";
    it[TextID::EnableUIZoomLabel] = "Attiva Zoom Interfaccia";
    it[TextID::UIZoomFactor] = "Imposta il livello di zoom per l'interfaccia utente";
    it[TextID::UIZoomFactorLabel] = "Fattore di Zoom Interfaccia";
    it[TextID::ResetZoomButton] = "Ripristina Zoom";

    // Profile Management
    it[TextID::UpdateCurrentProfileButton] = "Aggiorna Profilo Attuale";
    it[TextID::UpdateCurrentProfileTooltip] = "Salva le impostazioni attuali nel profilo attivo";

    // Definir as strings em russo
    auto &ru = m_strings[Language::Russian];

    // Tooltips - Aimbot
    ru[TextID::Aimbot] = "Активирует систему автоматического прицеливания, которая фиксируется на ближайшей цели";
    ru[TextID::Smoothing] = "Контролирует плавность движения аимбота (выше = плавнее)";
    ru[TextID::FocusLowestHealth] = "Приоритизирует цели с наименьшим здоровьем вместо ближайшего расстояния";
    ru[TextID::ShowAimbotFOVCircle] = "Показывает круг на экране, представляющий поле зрения аимбота";
    ru[TextID::AimbotFOVRadius] = "Устанавливает размер радиуса круга FOV аимбота";

    // Tooltips - Bullet TP
    ru[TextID::BulletTP] = "Активирует систему телепортации пуль для автоматического поражения целей";
    ru[TextID::ShowBulletTPFOVCircle] = "Показывает круг на экране, представляющий поле зрения Bullet TP";
    ru[TextID::BulletTPFOVRadius] = "Устанавливает размер радиуса круга FOV Bullet TP";
    ru[TextID::BulletTPTarget] = "Выбирает, какая часть противника будет целью для Bullet TP";
    ru[TextID::MinSafeDistance] = "Минимальное расстояние для безопасной телепортации пуль";
    ru[TextID::MaxSafeDistance] = "Максимальное расстояние для регулировки телепортации пуль";

    // Tooltips - Keybinds
    ru[TextID::UseGamepad] = "Включает поддержку геймпада для активации аимбота и bullet TP";
    ru[TextID::AimbotKey] = "Устанавливает клавишу, используемую для активации аимбота";
    ru[TextID::BulletTPKey] = "Устанавливает клавишу, используемую для активации Bullet TP";

    // Tooltips - Checks
    ru[TextID::SelfCheck] = "Предотвращает применение функций к самому себе";
    ru[TextID::VisibleCheck] = "Проверяет, видна ли цель перед применением функций";
    ru[TextID::UseLineOfSight] = "Использует LineOfSight для более точной проверки видимости, более надежно обнаруживая противников за стенами";
    ru[TextID::TeamCheck] = "Игнорирует членов команды при использовании функций, таких как Aimbot, ESP и Bullet TP";

    // Tooltips - ESP
    ru[TextID::TracerLines] = "Рисует линии, соединяющие центр экрана с вражескими игроками";
    ru[TextID::ShowHealth] = "Показывает количество здоровья вражеских игроков";
    ru[TextID::ShowDistance] = "Показывает расстояние между вами и вражескими игроками";
    ru[TextID::ShowUltimatePercentage] = "Показывает процент заряда ультимейта вражеских игроков";
    ru[TextID::EnableGlow] = "Активирует эффект свечения на вражеских игроках, позволяя видеть их сквозь стены";

    // Tooltips - FOV
    ru[TextID::FOVChanger] = "Изменяет поле зрения игры для более широкой перспективы";
    ru[TextID::FOVSlider] = "Устанавливает значение поля зрения (выше = шире обзор)";

    // Tooltips - Config
    ru[TextID::Language] = "Изменяет язык интерфейса";
    ru[TextID::SaveConfig] = "Сохраняет текущую конфигурацию в файл";
    ru[TextID::LoadConfig] = "Загружает конфигурацию из файла";
    ru[TextID::DeleteConfig] = "Удаляет сохраненный файл конфигурации";

    // Section Headers
    ru[TextID::AimbotSettings] = "НАСТРОЙКИ АИМБОТА";
    ru[TextID::SmoothingSettings] = "НАСТРОЙКИ СГЛАЖИВАНИЯ";
    ru[TextID::BulletTPSettings] = "НАСТРОЙКИ BULLET TP";

    ru[TextID::KeybindSettings] = "НАСТРОЙКИ КЛАВИШ";
    ru[TextID::CheckSettings] = "НАСТРОЙКИ ПРОВЕРОК";
    ru[TextID::ESPSettings] = "НАСТРОЙКИ ESP";
    ru[TextID::FOVSettings] = "НАСТРОЙКИ FOV";
    ru[TextID::ConfigSettings] = "НАСТРОЙКИ";
    ru[TextID::AdvancedBulletTPSettings] = "РАСШИРЕННЫЕ НАСТРОЙКИ BULLET TP";
    ru[TextID::AvailableConfigurations] = "ДОСТУПНЫЕ КОНФИГУРАЦИИ";
    ru[TextID::CurrentConfig] = "Текущая Конфигурация";

    // Menu Tabs
    ru[TextID::TabAimbotBulletTP] = "AIMBOT & BULLET TP";
    ru[TextID::TabVisuals] = "ВИЗУАЛЬНЫЕ";
    ru[TextID::TabMisc] = "РАЗНОЕ";
    ru[TextID::TabConfig] = "КОНФИГУРАЦИЯ";

    // Menu Items
    ru[TextID::AimbotLabel] = "Aimbot";
    ru[TextID::SmoothingLabel] = "Сглаживание";
    ru[TextID::FocusLowestHealthLabel] = "Фокус на Низком Здоровье";
    ru[TextID::ShowAimbotFOVCircleLabel] = "Показать Круг FOV Аимбота";
    ru[TextID::AimbotFOVRadiusLabel] = "Радиус FOV Аимбота";
    ru[TextID::BulletTPLabel] = "Bullet TP";
    ru[TextID::ShowBulletTPFOVCircleLabel] = "Показать Круг FOV Bullet TP";
    ru[TextID::BulletTPFOVRadiusLabel] = "Радиус FOV Bullet TP";
    ru[TextID::BulletTPTargetLabel] = "Цель Bullet TP";
    ru[TextID::HeadOption] = "Голова";
    ru[TextID::NeckOption] = "Шея";
    ru[TextID::RandomOption] = "Случайно";
    ru[TextID::AdvancedBulletTPSettingsLabel] = "Расширенные Настройки Bullet TP";
    ru[TextID::MinSafeDistanceLabel] = "Минимальная Безопасная Дистанция";
    ru[TextID::MaxSafeDistanceLabel] = "Максимальная Безопасная Дистанция";

    ru[TextID::UseGamepadLabel] = "Использовать Геймпад";
    ru[TextID::AimbotKeyLabel] = "Клавиша Аимбота";
    ru[TextID::BulletTPKeyLabel] = "Клавиша Bullet TP";
    ru[TextID::SelfCheckLabel] = "Самопроверка";
    ru[TextID::VisibleCheckLabel] = "Проверка Видимости";
    ru[TextID::UseLineOfSightLabel] = "Использовать LineOfSight (Лучшая Точность)";
    ru[TextID::TeamCheckLabel] = "Проверка Команды";
    ru[TextID::TracerLinesLabel] = "Линии Трассировки";
    ru[TextID::ShowHealthLabel] = "Здоровье";
    ru[TextID::ShowDistanceLabel] = "Расстояние";
    ru[TextID::ShowUltimatePercentageLabel] = "Процент Ультимейта";
    ru[TextID::EnableGlowLabel] = "Включить Свечение";
    ru[TextID::FOVChangerLabel] = "Изменить FOV";
    ru[TextID::FOVSliderLabel] = "Регулятор FOV";
    ru[TextID::LanguageLabel] = "Язык";
    ru[TextID::SaveConfigLabel] = "Сохранить Конфигурацию";
    ru[TextID::LoadConfigLabel] = "Загрузить Конфигурацию";
    ru[TextID::DeleteConfigLabel] = "Удалить Конфигурацию";
    ru[TextID::ConfigNameLabel] = "Имя Конфигурации";
    ru[TextID::SaveButtonLabel] = "Сохранить";
    ru[TextID::LoadButtonLabel] = "Загрузить";
    ru[TextID::DeleteButtonLabel] = "Удалить";

    // Role Target
    ru[TextID::RoleTargetSettings] = "НАСТРОЙКИ ЦЕЛИ ПО РОЛИ";
    ru[TextID::EnableRoleTargetLabel] = "Включить Цель по Роли";
    ru[TextID::TargetRoleLabel] = "Целевая Роль";
    ru[TextID::AllRolesOption] = "Все Роли";
    ru[TextID::TankOption] = "Танк";
    ru[TextID::DamageOption] = "Урон";
    ru[TextID::SupportOption] = "Поддержка";

    // Tabs
    ru[TextID::KeybindsAndChecksTab] = "КЛАВИШИ & ПРОВЕРКИ";

    // App Title
    ru[TextID::AppTitle] = "Rivals UnKnoWn";

    // Config UI
    ru[TextID::CurrentConfigLabel] = "Текущая Конфигурация:";
    ru[TextID::RefreshListButton] = "Обновить Список";
    ru[TextID::AvailableConfigurationsLabel] = "Доступные Конфигурации:";
    ru[TextID::SaveButton] = "Сохранить";
    ru[TextID::LoadButton] = "Загрузить";
    ru[TextID::DeleteButton] = "Удалить";

    // Advanced Settings
    ru[TextID::DelaySettingsLabel] = "НАСТРОЙКИ ЗАДЕРЖКИ";

    // Humanization System
    ru[TextID::EnableHumanizedTargetingLabel] = "Включить Гуманизированное Прицеливание";
    ru[TextID::EnableHumanizedTargetingTooltip] = "Включает или отключает систему гуманизированного прицеливания";
    ru[TextID::BodyPartProbabilitiesLabel] = "Вероятности Частей Тела (0.0-1.0):";
    ru[TextID::ProbabilitiesZeroInfoLabel] = "Инфо: Все вероятности равны нулю. Будет использовано равное распределение.";
    ru[TextID::ProbabilitiesNormalizedInfoLabel] = "Инфо: Вероятности будут нормализованы (Всего: %.2f)";
    ru[TextID::DynamicTimingLabel] = "Динамическое Время (секунды):";
    ru[TextID::MinChangeTimeLabel] = "Минимальное Время Изменения";
    ru[TextID::MinChangeTimeTooltip] = "Минимальное время между изменениями частей тела в гуманизированном режиме";
    ru[TextID::MaxChangeTimeLabel] = "Максимальное Время Изменения";
    ru[TextID::MaxChangeTimeTooltip] = "Максимальное время между изменениями частей тела в гуманизированном режиме";
    ru[TextID::RandomModeInfoLabel] = "Примечание: Случайный режим использует переменные интервалы (2-4 секунды)";
    ru[TextID::InitialValueFromInertiaLabel] = "Начальное значение: %.1f (от Фактора Инерции)";
    ru[TextID::InitialValueFromSmoothingLabel] = "Начальное значение: %.1f (от Сглаживания)";
    ru[TextID::InitialValueFromPitchSmoothingLabel] = "Начальное значение: %.1f (от Сглаживания Pitch)";
    ru[TextID::InitialValueFromYawSmoothingLabel] = "Начальное значение: %.1f (от Сглаживания Yaw)";
    ru[TextID::AdaptiveSmoothingLabel] = "Адаптивное Сглаживание";
    ru[TextID::NoneKeyLabel] = "Нет";

    // Role Target Tooltips
    ru[TextID::EnableRoleTargetTooltip] = "Активирует нацеливание только на определенные роли героев (Танк, Урон, Поддержка)";
    ru[TextID::TargetRoleTooltip] = "Выберите, на какую роль будет нацелен Aimbot и Bullet TP\nЕсли цель выбранной роли не найдена, будет использоваться порядок: Поддержка > Урон > Танк";

    // Support Settings
    ru[TextID::SupportSettingsLabel] = "НАСТРОЙКИ ПОДДЕРЖКИ";

    // Team Target Mode
    ru[TextID::TeamTargetModeLabel] = "Режим Цели Команды";
    ru[TextID::TeamTargetModeTooltip] = "Когда включено, нацеливается на союзников вместо врагов. Полезно для игроков поддержки";
    ru[TextID::TeamTargetKeyLabel] = "Клавиша Цели Команды";
    ru[TextID::TeamTargetKeyTooltip] = "Клавиша для переключения между нацеливанием на врагов или союзников";

    // Support Vision
    ru[TextID::SupportVisionLabel] = "Зрение Поддержки (Обзор 360°)";
    ru[TextID::SupportVisionTooltip] = "Игнорирует ограничения поля зрения, позволяя нацеливаться на союзников/врагов в любом направлении";

    // Flying Target Priority
    ru[TextID::FlyingTargetPriorityLabel] = "Приоритет Летающих Целей";
    ru[TextID::FlyingTargetPriorityTooltip] = "Отдает приоритет целям, находящимся в воздухе (прыгающим, летающим и т.д.)";
    ru[TextID::FlyingVelocityThresholdLabel] = "Порог Скорости Полета";
    ru[TextID::FlyingVelocityThresholdTooltip] = "Минимальная вертикальная скорость для рассмотрения цели как летающей";

    // UI Zoom Settings
    ru[TextID::UIZoomSettings] = "НАСТРОЙКИ МАСШТАБА ИНТЕРФЕЙСА";
    ru[TextID::EnableUIZoom] = "Включает масштабирование интерфейса с помощью CTRL+Колесо мыши";
    ru[TextID::EnableUIZoomLabel] = "Включить Масштаб Интерфейса";
    ru[TextID::UIZoomFactor] = "Устанавливает уровень масштаба для пользовательского интерфейса";
    ru[TextID::UIZoomFactorLabel] = "Фактор Масштаба Интерфейса";
    ru[TextID::ResetZoomButton] = "Сбросить Масштаб";

    // Profile Management
    ru[TextID::UpdateCurrentProfileButton] = "Обновить Текущий Профиль";
    ru[TextID::UpdateCurrentProfileTooltip] = "Сохраняет текущие настройки в активном профиле";
}

const char *LanguageSystem::GetText(TextID id) const
{
    // Verificar se o idioma atual existe no mapa
    auto langIt = m_strings.find(m_currentLanguage);
    if (langIt != m_strings.end())
    {
        // Verificar se o ID de texto existe para este idioma
        auto textIt = langIt->second.find(id);
        if (textIt != langIt->second.end())
        {
            return textIt->second.c_str();
        }
    }

    // Fallback para inglês se o texto não for encontrado no idioma atual
    auto enIt = m_strings.find(Language::English);
    if (enIt != m_strings.end())
    {
        auto textIt = enIt->second.find(id);
        if (textIt != enIt->second.end())
        {
            return textIt->second.c_str();
        }
    }

    // Retornar uma string vazia se o texto não for encontrado em nenhum idioma
    static const std::string empty;
    return empty.c_str();
}

void LanguageSystem::SetLanguage(Language lang)
{
    m_currentLanguage = lang;
}

const char *LanguageSystem::GetCurrentLanguageName() const
{
    auto it = m_languageNames.find(m_currentLanguage);
    if (it != m_languageNames.end())
    {
        return it->second;
    }
    return "Unknown";
}
