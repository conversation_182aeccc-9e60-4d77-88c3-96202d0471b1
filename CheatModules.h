#ifndef CHEAT_FEATURES_H
#define CHEAT_FEATURES_H

#include "Globals.h"
#include "KeyCaptureSystem.h"

// Função removida - usando o novo sistema de suavização
#include <limits>
#include <cfloat>
#include <cstdint>
#include <windows.h>
#include <algorithm> // Para std::min
#include <vector>    // Para std::vector
#include <direct.h>  // Para _mkdir
#include <sstream>   // Para std::stringstream

// Incluir o arquivo que contém a definição do enum EHeroRole
#include "Game/SDK/SDK/Marvel_structs.hpp"

namespace CheatFeatures
{
    // Constantes para velocidade de projétil
    constexpr float DEFAULT_PROJECTILE_SPEED = 18000.0f;  // Velocidade padrão de fallback
    constexpr float MIN_VALID_PROJECTILE_SPEED = 100.0f;  // Velocidade mínima considerada válida
    constexpr float MIN_TABLE_PROJECTILE_SPEED = 10.0f;   // Velocidade mínima da tabela

    // Função auxiliar para validar velocidade
    inline bool IsValidProjectileSpeed(float speed, float minSpeed = MIN_VALID_PROJECTILE_SPEED) {
        return speed >= minSpeed;
    }

    // Declarações antecipadas de funções
    float GetProjectileSpeedFromTable(int ProjectileID, bool bGetSource = true);
    float GetProjectileSpeedFromRootComponent(SDK::UWorld* World, SDK::AMarvelBaseCharacter* LocalCharacter);
    float GetProjectileSpeedFromMarvelAbilityTargetActor(SDK::UWorld* World, SDK::AMarvelBaseCharacter* LocalCharacter);
    float GetCurrentHeroProjectileSpeed(const char* FunctionName = "GetCurrentHeroProjectileSpeed");

    // Função para verificar se um jogador está visível usando LineOfSight com tratamento de exceção
    bool IsPlayerVisible(APlayerController *PlayerController, AMarvelBaseCharacter *TargetPlayer, FVector TargetLocation)
    {
        // Verificação robusta de ponteiros
        if (!IsValidObjectPtr(PlayerController) || !IsValidObjectPtr(TargetPlayer))
            return false;

        // Verificar se a posição da câmera é válida
        if (Variables::CameraLocation.IsZero())
            return false;

        // Usar SEH para evitar crashes durante a verificação de visibilidade
        __try
        {
            // Verificar se o jogador está visível usando LineOfSight
            // LineOfSight verifica se há uma linha de visão direta entre dois pontos
            // Retorna true se o alvo estiver visível, false caso contrário
            return PlayerController->LineOfSightTo(TargetPlayer, Variables::CameraLocation, true);
        }
        __except (EXCEPTION_EXECUTE_HANDLER)
        {
            // Em caso de exceção, registrar o erro e retornar false
            SafetySystem::LogError("IsPlayerVisible", "Exceção ao verificar visibilidade");
            return false;
        }
    }

    // Função para verificar se está em jogo (in-game) com tratamento de exceção
    bool IsInGame()
    {
        // Usar cache para evitar chamadas repetidas
        static float lastCheckTime = 0.0f;
        static bool lastResult = false;
        static float cacheValidityTime = 1.0f; // Cache válido por 1 segundo

        // Obter o tempo atual
        float currentTime = 0.0f;

        // Verificar se podemos usar o cache
        __try
        {
            if (IsValidObjectPtr(Variables::World) && IsValidObjectPtr(SDK::UGameplayStatics::StaticClass()))
            {
                currentTime = SDK::UGameplayStatics::GetTimeSeconds(Variables::World);

                // Se o cache ainda for válido, retornar o resultado em cache
                if (currentTime - lastCheckTime < cacheValidityTime)
                    return lastResult;
            }
            else
            {
                return false;
            }
        }
        __except (EXCEPTION_EXECUTE_HANDLER)
        {
            SafetySystem::LogError("IsInGame", "Exceção ao verificar tempo atual");
            return false;
        }

        // Verificar se temos acesso às classes necessárias
        auto gameplayStatics = (SDK::UGameplayStatics *)SDK::UGameplayStatics::StaticClass();
        if (!IsValidObjectPtr(gameplayStatics) || !IsValidObjectPtr(Variables::World))
            return false;

        // Obter o tempo de jogo em segundos com tratamento de exceção
        float gameTime = 0.0f;
        __try
        {
            gameTime = gameplayStatics->GetTimeSeconds(Variables::World);
        }
        __except (EXCEPTION_EXECUTE_HANDLER)
        {
            SafetySystem::LogError("IsInGame", "Exceção ao obter tempo de jogo");
            return false;
        }

        // Atualizar o cache
        lastCheckTime = currentTime;
        lastResult = (gameTime > 10.0f);

        // Considerar "em jogo" se o tempo for maior que 10 segundos
        return lastResult;
    }

    //---------------------------------------------------------------------
    // 		⚔️	Verificar se jogador é inimigo usando SDK direto
    //---------------------------------------------------------------------
    bool IsEnemy(SDK::APlayerState *EnemyPlayerState)
    {
        // Verificação robusta de ponteiros
        if (!IsValidObjectPtr(EnemyPlayerState))
            return false;

        // Verificar se o jogador local é válido
        if (!IsValidObjectPtr(Variables::AcknowledgedPawn))
            return false;

        // Obter o personagem do inimigo
        SDK::AMarvelBaseCharacter *EnemyCharacter = static_cast<SDK::AMarvelBaseCharacter *>(EnemyPlayerState->GetPawn());

        // Se não conseguir obter o personagem, retornar false
        if (!IsValidObjectPtr(EnemyCharacter))
            return false;

        // Usar o SDK::UTeamFunctionLibrary::IsAlly para verificar se é aliado
        // Sem usar __try/__except para evitar problemas com liberação de objetos
        bool bIsAlly = SDK::UTeamFunctionLibrary::IsAlly(Variables::AcknowledgedPawn, EnemyCharacter, true);

        // Retornar true se NÃO for aliado (ou seja, é inimigo)
        return !bIsAlly;
    }

    //---------------------------------------------------------------------
    // 		🚀	Obter velocidade usando SDK direto (sem offsets hardcoded)
    //---------------------------------------------------------------------
    SDK::FVector GetVelocity(SDK::AMarvelBaseCharacter *Character)
    {
        // Verificação robusta de ponteiros
        if (!IsValidObjectPtr(Character))
            return SDK::FVector();

        // Usar o SDK diretamente para obter o componente de movimento
        SDK::UCharacterMovementComponent* MovementComponent = Character->CharacterMovement;
        if (!IsValidObjectPtr(MovementComponent))
            return SDK::FVector();

        // Obter a velocidade diretamente da propriedade pública Velocity
        SDK::FVector Velocity = MovementComponent->Velocity;

        // Verificar se a velocidade é válida (não contém NaN ou valores extremos)
        if (isnan(Velocity.X) || isnan(Velocity.Y) || isnan(Velocity.Z) ||
            isinf(Velocity.X) || isinf(Velocity.Y) || isinf(Velocity.Z) ||
            fabs(Velocity.X) > 10000.0f || fabs(Velocity.Y) > 10000.0f || fabs(Velocity.Z) > 10000.0f)
        {
            return SDK::FVector(); // Retornar vetor zero se a velocidade for inválida
        }

        return Velocity;
    }

    //---------------------------------------------------------------------
    // 		📋	Obter nome do herói usando SDK direto (sem offsets hardcoded)
    //---------------------------------------------------------------------
    std::string GetActorNameFromID(uint32_t heroID)
    {
        // Verificar se o ID é válido
        if (heroID == 0)
            return "Unknown";

        // Usar o SDK diretamente para obter os dados do herói
        SDK::FMarvelHeroTable HeroData = SDK::UHeroTableFuncLibrary::GetHeroData(heroID);

        // Verificar se conseguimos obter dados válidos e se TName não está vazio
        if (HeroData.HeroID == heroID)
        {
            // Tentar usar TName primeiro
            std::string heroName = HeroData.TName.ToString();
            if (!heroName.empty())
                return heroName;

            // Se TName estiver vazio, tentar usar EnName
            heroName = HeroData.EnName.ToString();
            if (!heroName.empty())
                return heroName;
        }

        return "Unknown";
    }

    //---------------------------------------------------------------------
    // 		🎯	Obter ID do herói usando SDK direto (sem offsets hardcoded)
    //---------------------------------------------------------------------
    uint32_t GetHeroID(SDK::APlayerState *PlayerState)
    {
        if (!IsValidObjectPtr(PlayerState))
            return 0; // ID inválido

        // Fazer cast para AMarvelPlayerState para acessar SelectedHeroID
        SDK::AMarvelPlayerState* MarvelPlayerState = static_cast<SDK::AMarvelPlayerState*>(PlayerState);
        if (!IsValidObjectPtr(MarvelPlayerState))
            return 0;

        // Usar o SDK diretamente para obter o SelectedHeroID
        return MarvelPlayerState->SelectedHeroID;
    }

    // Função para obter a role do personagem
    SDK::EHeroRole GetRole(SDK::AMarvelBaseCharacter *Player)
    {
        return Player ? Player->GetHeroRole() : SDK::EHeroRole::Unknown;
    }



    // Função para obter o nome do herói com base no ID
    const char *GetHeroName(uint32_t HeroID)
    {
        // Tentar obter o nome usando o sistema de nomes do jogo
        static std::string dynamicName;
        dynamicName = GetActorNameFromID(HeroID);
        if (dynamicName != "Unknown")
        {
            return dynamicName.c_str();
        }

        // Se não encontrar, retornar "Unknown"
        static char unknownHero[64];
        sprintf_s(unknownHero, "Unknown (ID: %u)", HeroID);
        return unknownHero;
    }











    // Função para obter informações detalhadas do herói atual
    void GetCurrentHeroInfo(char *buffer, size_t bufferSize)
    {
        if (!IsValidPtr(Variables::AcknowledgedPawn))
        {
            sprintf_s(buffer, bufferSize, "No hero selected");
            return;
        }

        APlayerState *LocalPlayerState = Variables::AcknowledgedPawn->PlayerState;
        if (!IsValidPtr(LocalPlayerState))
        {
            sprintf_s(buffer, bufferSize, "Invalid player state");
            return;
        }

        uint32_t HeroID = GetHeroID(LocalPlayerState);
        const char *HeroName = GetHeroName(HeroID);
        float ProjectileSpeed = GetCurrentHeroProjectileSpeed("GetCurrentHeroInfo");

        // Tentar obter o nome do herói usando o sistema de nomes do jogo
        std::string dynamicName = GetActorNameFromID(HeroID);

        // Se conseguiu obter o nome dinâmico
        if (dynamicName != "Unknown")
        {
            sprintf_s(buffer, bufferSize, "Hero: %s (ID: %d)\nProjectile Speed: %.1f",
                      dynamicName.c_str(), HeroID, ProjectileSpeed);
        }
        else
        {
            sprintf_s(buffer, bufferSize, "Hero: %s (ID: %d)\nProjectile Speed: %.1f (Default)",
                      HeroName, HeroID, ProjectileSpeed);
        }
    }

    //---------------------------------------------------------------------
    // 		🔍	Obter a velocidade do projétil usando o ProjectileTableFuncLibrary
    //---------------------------------------------------------------------
    float GetProjectileSpeedFromTable(int ProjectileID, bool bGetSource)
    {
        try {
            // Obter a tabela de dados do projétil
            SDK::FMarvelProjectileAgentTable ProjectileTable = SDK::UProjectileTableFuncLibrary::GetProjectileData(ProjectileID, bGetSource);

            // Obter a velocidade do projétil
            float FlySpeed = ProjectileTable.ProjectileAgent.FlySpeed;

            // Verificar se a velocidade é válida
            if (IsValidProjectileSpeed(FlySpeed, MIN_TABLE_PROJECTILE_SPEED)) {
                return FlySpeed;
            }

            return 0.0f;
        }
        catch (...) {
            SafetySystem::LogError("GetProjectileSpeedFromTable", "Exceção ao obter velocidade do projétil da tabela");
            return 0.0f;
        }
    }

    //---------------------------------------------------------------------
    // 		🚀	Obter velocidade do projétil do componente raiz
    //---------------------------------------------------------------------
    float GetProjectileSpeedFromRootComponent(SDK::UWorld* World, SDK::AMarvelBaseCharacter* LocalCharacter)
    {
        // Verificar parâmetros de entrada
        if (!IsValidPtr(World) || !IsValidObjectPtr(LocalCharacter)) {
            return 0.0f;
        }

        // Obter todos os projéteis no mundo
        SDK::TArray<SDK::AActor*> Bullets;
        SDK::UGameplayStatics::GetAllActorsOfClass(World, SDK::AGameplayAbilityTargetActor::StaticClass(), &Bullets);

        if (Bullets.Num() <= 0) {
            return 0.0f;
        }

        // Procurar por projéteis do jogador local
        for (int i = 0; i < Bullets.Num(); i++) {
            if (!Bullets.IsValidIndex(i))
                continue;

            auto Bullet = Bullets[i];
            if (!IsValidPtr(Bullet) || Bullet->GetOwner() != LocalCharacter)
                continue;

            // Verificar se o projétil tem um componente raiz válido
            if (!IsValidPtr(Bullet->RootComponent))
                continue;

            // Obter a velocidade do projétil
            SDK::FVector BulletVelocity = Bullet->RootComponent->GetComponentVelocity();
            float speed = BulletVelocity.Magnitude();

            // Verificar se a velocidade é válida
            if (IsValidProjectileSpeed(speed)) {
                Bullets.Clear();
                return speed;
            }
        }

        Bullets.Clear();
        return 0.0f;
    }

    //---------------------------------------------------------------------
    // 		🎯	Obter velocidade do projétil do MarvelAbilityTargetActor
    //---------------------------------------------------------------------
    float GetProjectileSpeedFromMarvelAbilityTargetActor(SDK::UWorld* World, SDK::AMarvelBaseCharacter* LocalCharacter)
    {
        try {
            // Verificar se temos um mundo válido
            if (!IsValidPtr(World)) {
                return 0.0f;
            }

            // Verificar se temos um personagem local válido
            if (!IsValidObjectPtr(LocalCharacter)) {
                return 0.0f;
            }

            // Obter todos os projéteis do tipo AMarvelAbilityTargetActor_Projectile
            SDK::TArray<SDK::AActor*> Projectiles;
            SDK::UGameplayStatics::GetAllActorsOfClass(
                World,
                SDK::AMarvelAbilityTargetActor_Projectile::StaticClass(),
                &Projectiles
            );

            for (int i = 0; i < Projectiles.Num(); i++) {
                SDK::AActor* Projectile = Projectiles[i];
                if (!IsValidPtr(Projectile))
                    continue;

                // Verificar se o projétil pertence ao jogador local
                SDK::AActor* Owner = nullptr;
                try {
                    Owner = Projectile->GetOwner();
                }
                catch (...) {
                    continue;
                }

                if (Owner != LocalCharacter) {
                    continue;
                }

                // Verificar explicitamente se o projétil é do tipo correto
                if (!Projectile->IsA(SDK::AMarvelAbilityTargetActor_Projectile::StaticClass())) {
                    // Projétil não é do tipo correto
                    continue;
                }

                // Converter para o tipo correto com segurança
                SDK::AMarvelAbilityTargetActor_Projectile* ProjectileActor = nullptr;
                try {
                    ProjectileActor = reinterpret_cast<SDK::AMarvelAbilityTargetActor_Projectile*>(Projectile);
                    if (!IsValidPtr(ProjectileActor)) {
                        // Falha na verificação após o cast
                        continue;
                    }
                } catch (...) {
                    SafetySystem::LogError("GetProjectileSpeedFromMarvelAbilityTargetActor", "Exception during cast to AMarvelAbilityTargetActor_Projectile");
                    continue;
                }

                // Obter os dados do projétil
                try {
                    SDK::FMarvelProjectileAgentTable ProjectileData = ProjectileActor->K2_GetProjectileData();
                    float FlySpeed = ProjectileData.ProjectileAgent.FlySpeed;

                    if (IsValidProjectileSpeed(FlySpeed)) {
                        Projectiles.Clear();
                        return FlySpeed;
                    }
                }
                catch (...) {
                    SafetySystem::LogError("GetProjectileSpeedFromMarvelAbilityTargetActor", "Exception when getting ProjectileData");
                    // Continuar para tentar outros métodos
                }

                // Verificar se o componente de movimento é válido
                if (IsValidPtr(ProjectileActor->MovementComponent)) {
                    float Speed = ProjectileActor->MovementComponent->InitialSpeed;

                    if (Speed < 100.0f) {
                        SDK::FVector BulletVelocity = ProjectileActor->MovementComponent->Velocity;
                        Speed = BulletVelocity.Magnitude();
                    }

                    if (IsValidProjectileSpeed(Speed)) {
                        Projectiles.Clear();
                        return Speed;
                    }
                }
            }

            Projectiles.Clear();
            return 0.0f;
        }
        catch (...) {
            SafetySystem::LogError("GetProjectileSpeedFromMarvelAbilityTargetActor", "Unknown exception");
            return 0.0f;
        }
    }

    //---------------------------------------------------------------------
    // 		🎯	Obter velocidade do projétil do GameplayAbilityTargetActor
    //---------------------------------------------------------------------
    float GetProjectileSpeedFromGameplayAbilityTargetActor(SDK::UWorld* World, SDK::AMarvelBaseCharacter* LocalCharacter)
    {
        // Verificar parâmetros de entrada
        if (!IsValidPtr(World) || !IsValidObjectPtr(LocalCharacter)) {
            return 0.0f;
        }

        // Obter todos os projéteis no mundo
        SDK::TArray<SDK::AActor*> Bullets;
        SDK::UGameplayStatics::GetAllActorsOfClass(World, SDK::AGameplayAbilityTargetActor::StaticClass(), &Bullets);

        if (Bullets.Num() <= 0) {
            return 0.0f;
        }

        // Procurar por projéteis do jogador local
        for (int i = 0; i < Bullets.Num(); i++) {
            if (!Bullets.IsValidIndex(i))
                continue;

            auto Bullet = Bullets[i];
            if (!IsValidPtr(Bullet) || Bullet->GetOwner() != LocalCharacter)
                continue;

            // Verificar se o projétil tem um componente raiz válido
            if (!IsValidPtr(Bullet->RootComponent))
                continue;

            // Obter a velocidade do projétil
            SDK::FVector BulletVelocity = Bullet->RootComponent->GetComponentVelocity();
            float speed = BulletVelocity.Magnitude();

            // Verificar se a velocidade é válida
            if (IsValidProjectileSpeed(speed)) {
                Bullets.Clear();
                return speed;
            }
        }

        Bullets.Clear();
        return 0.0f;
    }

    //---------------------------------------------------------------------
    // 		🔍	Obter velocidade do projétil do herói atual
    //---------------------------------------------------------------------
    float GetCurrentHeroProjectileSpeed(const char* FunctionName)
    {
        try {
            // Verificar se temos um personagem local válido
            if (!IsValidObjectPtr(Variables::AcknowledgedPawn)) {
                return DEFAULT_PROJECTILE_SPEED;
            }

            // Converter para AMarvelBaseCharacter
            SDK::AMarvelBaseCharacter* LocalCharacter = static_cast<SDK::AMarvelBaseCharacter*>(Variables::AcknowledgedPawn);
            if (!IsValidObjectPtr(LocalCharacter)) {
                return DEFAULT_PROJECTILE_SPEED;
            }

            // Verificar se temos um mundo válido
            if (!IsValidPtr(Variables::World)) {
                return DEFAULT_PROJECTILE_SPEED;
            }

            // Verificar se o PlayerState é válido
            if (!IsValidPtr(LocalCharacter->PlayerState)) {
                return DEFAULT_PROJECTILE_SPEED;
            }

            // Obter o ID do herói
            uint32_t HeroID = GetHeroID(LocalCharacter->PlayerState);
            if (HeroID == 0) {
                return DEFAULT_PROJECTILE_SPEED;
            }

            // 1. Tentar obter da tabela de dados do projétil
            float speed = GetProjectileSpeedFromTable(HeroID);
            if (IsValidProjectileSpeed(speed)) {
                return speed;
            }

            // 2. Tentar obter de projéteis específicos do Marvel
            speed = GetProjectileSpeedFromMarvelAbilityTargetActor(Variables::World, LocalCharacter);
            if (IsValidProjectileSpeed(speed)) {
                return speed;
            }

            // 3. Velocidade do componente raiz de qualquer projétil
            speed = GetProjectileSpeedFromRootComponent(Variables::World, LocalCharacter);
            if (IsValidProjectileSpeed(speed)) {
                return speed;
            }

            // 4. Projéteis do tipo GameplayAbilityTargetActor
            speed = GetProjectileSpeedFromGameplayAbilityTargetActor(Variables::World, LocalCharacter);
            if (IsValidProjectileSpeed(speed)) {
                return speed;
            }

            // Se nenhum método funcionar, retornar o valor padrão
            return DEFAULT_PROJECTILE_SPEED;
        }
        catch (const std::exception&) {
            SafetySystem::LogError(FunctionName, "Exception during projectile speed detection");
            return DEFAULT_PROJECTILE_SPEED;
        }
        catch (...) {
            SafetySystem::LogError(FunctionName, "Unknown exception");
            return DEFAULT_PROJECTILE_SPEED;
        }
    }

    // Função para obter a velocidade do projétil do jogador local
    float GetLocalPlayerProjectileSpeed()
    {
        if (!IsValidPtr(Variables::AcknowledgedPawn))
            return DEFAULT_PROJECTILE_SPEED;

        return GetCurrentHeroProjectileSpeed();
    }

    void DrawESP(TArray<AActor *> &ActorList, APlayerController *PlayerController, ImDrawList *BackgroundList, FName HeadSocketName)
    {
        // Verificar se o BackgroundList é válido usando safe_memory_check
        if (!IsValidPtr(BackgroundList))
        {
            SafetySystem::LogError("DrawESP", "BackgroundList is invalid");
            return;
        }

        // Verificar se o PlayerController é válido
        if (!IsValidPtr(PlayerController))
        {
            SafetySystem::LogError("DrawESP", "PlayerController is null");
            return;
        }

        // Verificar se a lista de atores é válida
        if (ActorList.Num() <= 0)
        {
            SafetySystem::LogError("DrawESP", "ActorList is empty");
            return;
        }

        // Usar loop seguro para evitar deadlocks durante gameplay
        SafeArrayLoop("DrawESP_Players", ActorList, [&](SDK::AActor* actor, int i) -> bool {
            auto Player = reinterpret_cast<AMarvelBaseCharacter *>(actor);
            if (!IsValidObjectPtr_Fast(Player)) // Usar validação rápida em loops
                return true; // Continuar loop

            if (mods::LocalCheck && Player->IsLocallyControlled())
                return true; // Continuar loop
            if (Player->GetCurrentHealth() <= 0.0f)
                return true; // Continuar loop

            if (mods::TeamCheck)
            {
                // Verificar se os personagens são válidos
                if (!IsValidObjectPtr_Fast(Variables::AcknowledgedPawn) || !IsValidObjectPtr_Fast(Player))
                    return true; // Continuar loop

                // Usar o SDK::UTeamFunctionLibrary::IsAlly para verificar se é aliado
                // Sem usar __try/__except para evitar problemas com liberação de objetos
                bool bIsAlly = SDK::UTeamFunctionLibrary::IsAlly(Variables::AcknowledgedPawn, Player, true);

                if (bIsAlly)
                {
                    return true; // Continuar loop - Skip same-team players
                }
            }

            float DistanceToPlayer = Player->GetDistanceTo(PlayerController);

            USkeletalMeshComponent *Mesh = Player->GetMesh();
            if (!IsValidPtr_Fast(Mesh) || !Mesh->SkeletalMesh)
                return true; // Continuar loop

            // Obter a posição da cabeça do jogador para tracer lines
            FVector TargetHead3D = Mesh->GetSocketLocation(HeadSocketName);
            if (TargetHead3D.IsZero())
                return true; // Continuar loop

            FVector2D TargetHead2D;
            if (!PlayerController->ProjectWorldLocationToScreen(TargetHead3D, &TargetHead2D, true))
                return true; // Continuar loop

            // Get foot position
            auto TargetFoot3D = Mesh->GetSocketLocation(UKismetStringLibrary::Conv_StringToName(FString(L"Root")));
            FVector2D TargetFoot2D = FVector2D();

            if (!PlayerController->ProjectWorldLocationToScreen(TargetFoot3D, &TargetFoot2D, true))
            {
                SafetySystem::LogError("DrawESP", "Failed to project TargetFoot3D to screen!");
                return true; // Continuar loop
            }

            auto Colour = Mesh->WasRecentlyRendered(SDK::ERecentlyRenderedType::OnScreen, 0.f) ? ImColor(0, 255, 0) : ImColor(0, 0, 255);

            if (Player && PlayerController)
            {
                if (Player->GetCurrentHealth() > 0.f)
                {
                    // Draw health bar
                    float HealthPercentage = Player->GetCurrentHealth() / Player->GetMaxHealth();
                    float HealthBarWidth = 50.0f;                                                             // Width of health bar
                    float HealthBarHeight = 3.0f;                                                             // Height of health bar
                    ImVec2 HealthBarStart = ImVec2(TargetFoot2D.X - HealthBarWidth / 2, TargetFoot2D.Y - 20); // Adjust position to foot
                    ImVec2 HealthBarEnd = ImVec2(HealthBarStart.x + HealthBarWidth * HealthPercentage, HealthBarStart.y + HealthBarHeight);
                    BackgroundList->AddRectFilled(HealthBarStart, HealthBarEnd, mods::HealthBarColor); // Apply health bar color

                    if (mods::ShowHealth)
                    {
                        // Display health without decimal places
                        std::string health = std::to_string(static_cast<int>(Player->GetCurrentHealth())) + " / " + std::to_string(static_cast<int>(Player->GetMaxHealth()));
                        BackgroundList->AddText(ImVec2(TargetFoot2D.X - 20, TargetFoot2D.Y - 35), mods::HealthColor, health.c_str()); // Apply health color
                    }

                    if (mods::ShowDistance)
                    {
                        // Display distance without decimal places
                        std::string Distance = std::to_string(static_cast<int>(Player->GetDistanceTo(PlayerController) / 100)) + "m";
                        BackgroundList->AddText(ImVec2(TargetFoot2D.X - 20, TargetFoot2D.Y - 50), mods::DistanceColor, Distance.c_str()); // Apply distance color
                    }

                    if (mods::TracerLines)
                    {
                        BackgroundList->AddLine(ImVec2(Variables::ScreenCenter.X, 120), ImVec2(TargetHead2D.X, TargetHead2D.Y), Colour, 2.f);
                    }

                    // Exibir porcentagem de ultimate (real)
                    if (mods::showUltimatePercentage)
                    {
                        // Obter o ThreatValueAdmin para acessar as informações de ultimate
                        if (!Variables::ThreatValueAdmin && Variables::World)
                        {
                            // Tentar obter o ThreatValueAdmin se ainda não estiver disponível
                            Variables::ThreatValueAdmin = SDK::UMarvelAudioLibrary::GetThreatValueAdmin(Variables::World);
                        }

                        if (Variables::ThreatValueAdmin)
                        {
                            // Obter as informações de ameaça dos jogadores
                            auto ThreatInfoArray = Variables::ThreatValueAdmin->GetPlayerThreatInfo();

                            // Procurar o jogador atual na lista de ameaças
                            for (int j = 0; j < ThreatInfoArray.Num(); j++)
                            {
                                if (ThreatInfoArray[j].Character == Player)
                                {
                                    // Formatar a string da porcentagem de ultimate
                                    char ultimateStr[64];
                                    sprintf_s(ultimateStr, "Ult: %.0f%%", ThreatInfoArray[j].UltimatePercentage);

                                    // Adicionar o texto abaixo da barra de saúde
                                    BackgroundList->AddText(ImVec2(TargetFoot2D.X - 20, TargetFoot2D.Y - 65), IM_COL32(0, 255, 255, 255), ultimateStr);
                                    break;
                                }
                            }
                        }
                    }
                }
            }

            return true; // Continuar processando próximo elemento
        });
    }

    //---------------------------------------------------------------------
    // 		🎯	Selecionar alvo usando SDK direto
    //---------------------------------------------------------------------
    SDK::AMarvelBaseCharacter *SelectTarget(SDK::TArray<SDK::AActor *> &ActorList, SDK::APlayerController *PlayerController, SDK::FName HeadSocketName, float actualFovCircle)
    {
        double ClosestDistanceToCenter = DBL_MAX;
        float LowestHealth = FLT_MAX;
        SDK::AMarvelBaseCharacter *SelectedTarget = nullptr;

        // Estruturas para armazenar alvos de fallback por role
        struct TargetInfo
        {
            SDK::AMarvelBaseCharacter *Player;
            double DistanceToCenter;
            float Health;
            SDK::EHeroRole Role;
        };

        TargetInfo SupportTarget = {nullptr, DBL_MAX, FLT_MAX, SDK::EHeroRole::Unknown};
        TargetInfo DamageTarget = {nullptr, DBL_MAX, FLT_MAX, SDK::EHeroRole::Unknown};
        TargetInfo TankTarget = {nullptr, DBL_MAX, FLT_MAX, SDK::EHeroRole::Unknown};

        // Estrutura para armazenar o melhor alvo voador
        TargetInfo FlyingTarget = {nullptr, DBL_MAX, FLT_MAX, SDK::EHeroRole::Unknown};
        bool foundFlyingTarget = false;

        // Flag para indicar se encontramos um alvo da role selecionada
        bool foundTargetWithSelectedRole = false;

        for (int i = 0; i < static_cast<int>(ActorList.Num()); i++)
        {
            if (!ActorList.IsValidIndex(i))
                continue;

            auto Player = static_cast<SDK::AMarvelBaseCharacter *>(ActorList[i]);
            if (!IsValidObjectPtr(Player)) // IsValidObjectPtr já inclui IsValidPtr
                continue;

            if (mods::LocalCheck && Player->IsLocallyControlled())
                continue;
            if (Player->GetCurrentHealth() <= 0.0f) // Validação redundante removida
                continue;

            if (mods::TeamCheck)
            {
                // Verificar se os personagens são válidos
                if (!IsValidObjectPtr(Variables::AcknowledgedPawn) || !IsValidObjectPtr(Player))
                    continue;

                // Usar o SDK::UTeamFunctionLibrary::IsAlly para verificar se é aliado
                // Sem usar __try/__except para evitar problemas com liberação de objetos
                bool bIsAlly = SDK::UTeamFunctionLibrary::IsAlly(Variables::AcknowledgedPawn, Player, true);

                // Se estiver no modo TeamTarget (modo suporte), queremos mirar em aliados
                // Se não estiver no modo TeamTarget (modo normal), queremos mirar em inimigos
                if ((mods::teamTargetMode && !bIsAlly) || (!mods::teamTargetMode && bIsAlly))
                {
                    continue; // Pular jogadores que não correspondem ao modo atual
                }
            }

            float DistanceToPlayer = Player->GetDistanceTo(PlayerController);

            USkeletalMeshComponent *Mesh = Player->GetMesh();
            if (!IsValidPtr(Mesh) || !Mesh->SkeletalMesh)
                continue;

            // Obter a posição da cabeça do jogador
            FVector TargetHead3D = Mesh->GetSocketLocation(HeadSocketName);
            if (TargetHead3D.IsZero())
                continue;

            // Verificar visibilidade usando o método antigo e o novo
            bool isVisibleOld = Mesh->WasRecentlyRendered(SDK::ERecentlyRenderedType::OnScreen, 0.f);
            bool isVisibleNew = IsPlayerVisible(PlayerController, Player, TargetHead3D);

            // Escolher o método de verificação de visibilidade com base na opção do usuário
            bool isVisible = mods::UseLineOfSight ? isVisibleNew : isVisibleOld;

            if (mods::VisCheck && !isVisible)
                continue;

            FVector2D TargetHead2D;
            if (!PlayerController->ProjectWorldLocationToScreen(TargetHead3D, &TargetHead2D, true))
                continue;

            // Verificar se o alvo está dentro do FOV ou se a Visão de Suporte está ativada
            if (InCircle(actualFovCircle, TargetHead2D) || mods::isSupportVisionEnabled)
            {
                double DistanceToCenter = UKismetMathLibrary::Vector_Distance2D(
                    FVector(Variables::ScreenCenter.X, Variables::ScreenCenter.Y, 0),
                    FVector(TargetHead2D.X, TargetHead2D.Y, 0));
                float CurrentHealth = Player->GetCurrentHealth();

                // Verificar se o alvo está voando (para priorização de alvos voadores)
                SDK::FVector velocity = GetVelocity(Player);
                bool isFlying = false;

                if (mods::shouldPrioritizeFlyingTargets && abs(velocity.Z) > mods::flyingVelocityThreshold)
                {
                    isFlying = true;

                    // Armazenar como alvo voador se for melhor que o atual
                    if ((mods::focusLowestHealth && CurrentHealth < FlyingTarget.Health) ||
                        (!mods::focusLowestHealth && DistanceToCenter < FlyingTarget.DistanceToCenter))
                    {
                        FlyingTarget.Player = Player;
                        FlyingTarget.DistanceToCenter = DistanceToCenter;
                        FlyingTarget.Health = CurrentHealth;
                        FlyingTarget.Role = GetRole(Player);
                        foundFlyingTarget = true;
                    }
                }

                // Verificar se o filtro de role está ativado
                if (mods::enableRoleTarget && mods::targetRole > 0)
                {
                    // Obter a role do jogador
                    SDK::EHeroRole playerRole = GetRole(Player);
                    SDK::EHeroRole targetRole;

                    // Converter o valor do targetRole para o enum EHeroRole
                    switch (mods::targetRole)
                    {
                    case 1:
                        targetRole = SDK::EHeroRole::Tank;
                        break;
                    case 2:
                        targetRole = SDK::EHeroRole::Damage;
                        break;
                    case 3:
                        targetRole = SDK::EHeroRole::Support;
                        break;
                    default:
                        targetRole = SDK::EHeroRole::Unknown;
                        break;
                    }

                    // Se a role do jogador corresponder à role alvo, selecionar como alvo principal
                    if (playerRole == targetRole)
                    {
                        foundTargetWithSelectedRole = true;

                        if (mods::focusLowestHealth)
                        {
                            if (CurrentHealth < LowestHealth)
                            {
                                LowestHealth = CurrentHealth;
                                SelectedTarget = Player;
                                ClosestDistanceToCenter = DistanceToCenter;
                            }
                        }
                        else
                        {
                            if (DistanceToCenter < ClosestDistanceToCenter)
                            {
                                ClosestDistanceToCenter = DistanceToCenter;
                                SelectedTarget = Player;
                                LowestHealth = CurrentHealth;
                            }
                        }
                    }
                    // Caso contrário, armazenar como possível fallback
                    else
                    {
                        // Armazenar o jogador como fallback com base em sua role
                        switch (playerRole)
                        {
                        case SDK::EHeroRole::Support:
                            if ((mods::focusLowestHealth && CurrentHealth < SupportTarget.Health) ||
                                (!mods::focusLowestHealth && DistanceToCenter < SupportTarget.DistanceToCenter))
                            {
                                SupportTarget.Player = Player;
                                SupportTarget.DistanceToCenter = DistanceToCenter;
                                SupportTarget.Health = CurrentHealth;
                            }
                            break;

                        case SDK::EHeroRole::Damage:
                            if ((mods::focusLowestHealth && CurrentHealth < DamageTarget.Health) ||
                                (!mods::focusLowestHealth && DistanceToCenter < DamageTarget.DistanceToCenter))
                            {
                                DamageTarget.Player = Player;
                                DamageTarget.DistanceToCenter = DistanceToCenter;
                                DamageTarget.Health = CurrentHealth;
                            }
                            break;

                        case SDK::EHeroRole::Tank:
                            if ((mods::focusLowestHealth && CurrentHealth < TankTarget.Health) ||
                                (!mods::focusLowestHealth && DistanceToCenter < TankTarget.DistanceToCenter))
                            {
                                TankTarget.Player = Player;
                                TankTarget.DistanceToCenter = DistanceToCenter;
                                TankTarget.Health = CurrentHealth;
                            }
                            break;

                        default:
                            break;
                        }
                    }
                }
                // Se o filtro de role não estiver ativado, usar a lógica normal
                else
                {
                    if (mods::focusLowestHealth)
                    {
                        if (CurrentHealth < LowestHealth)
                        {
                            LowestHealth = CurrentHealth;
                            SelectedTarget = Player;
                            ClosestDistanceToCenter = DistanceToCenter;
                        }
                    }
                    else
                    {
                        if (DistanceToCenter < ClosestDistanceToCenter)
                        {
                            ClosestDistanceToCenter = DistanceToCenter;
                            SelectedTarget = Player;
                            LowestHealth = CurrentHealth;
                        }
                    }
                }
            }
        }

        // Priorizar alvos voadores se a opção estiver ativada
        if (mods::shouldPrioritizeFlyingTargets && foundFlyingTarget && IsValidObjectPtr(FlyingTarget.Player))
        {
            return FlyingTarget.Player;
        }

        // Se não encontramos um alvo da role selecionada, usar o sistema de fallback
        if (mods::enableRoleTarget && mods::targetRole > 0 && !foundTargetWithSelectedRole)
        {
            // Ordem de prioridade: Support > Damage > Tank
            if (IsValidObjectPtr(SupportTarget.Player))
            {
                return SupportTarget.Player;
            }
            else if (IsValidObjectPtr(DamageTarget.Player))
            {
                return DamageTarget.Player;
            }
            else if (IsValidObjectPtr(TankTarget.Player))
            {
                return TankTarget.Player;
            }
        }

        return SelectedTarget;
    }

    //---------------------------------------------------------------------
    // 		🔄	Verificação de Tecla para Team Target Mode
    //---------------------------------------------------------------------
    void CheckTeamTargetKey(SDK::APlayerController *PlayerController)
    {
        if (!IsValidObjectPtr(PlayerController))
            return;

        // Verificar se a tecla de Team Target Mode está pressionada
        bool isTeamTargetKeyPressed = false;

        // Verificar teclado/mouse
        bool isKeyboardMousePressed = PlayerController->IsInputKeyDown(Keys::CurrentTeamTargetKey);

        // Verificar gamepad
        SDK::FKey *teamTargetGamepadButton = Keys::GetGamepadButtonByIndex(mods::gamepadTeamTargetButton);
        bool isGamepadPressed = false;
        if (IsValidPtr(teamTargetGamepadButton))
        {
            isGamepadPressed = PlayerController->IsInputKeyDown(*teamTargetGamepadButton);
        }

        // Ativar se qualquer um dos inputs estiver pressionado
        isTeamTargetKeyPressed = isKeyboardMousePressed || isGamepadPressed;

        // Se as teclas funcionaram corretamente, resetar o contador de erros
        if (isKeyboardMousePressed || isGamepadPressed) {
            KeyCaptureSystem::ResetErrorCount();
        }

        // Verificar se a tecla foi pressionada (toggle)
        static bool wasTeamTargetKeyPressed = false;
        if (isTeamTargetKeyPressed && !wasTeamTargetKeyPressed)
        {
            // Alternar o modo de alvo
            mods::teamTargetMode = !mods::teamTargetMode;

            // Exibir mensagem no console
            printf("Team Target Mode: %s\n", mods::teamTargetMode ? "Aliados (Suporte)" : "Inimigos (Normal)");
        }

        // Atualizar o estado anterior da tecla
        wasTeamTargetKeyPressed = isTeamTargetKeyPressed;
    }

    //---------------------------------------------------------------------
    // 		🎯	Sistema de Aimbot Avançado com Inércia, Smoothing Adaptativo e Humanização
    //---------------------------------------------------------------------
    void RunAimbot(SDK::UWorld *World, SDK::APlayerController *PlayerController, SDK::APlayerCameraManager *PlayerCameraManager, SDK::AMarvelBaseCharacter *TargetPlayer, SDK::FName HeadSocketName, float actualFovCircle)
    {
        if (!mods::aimbot || !TargetPlayer || !IsValidPtr(TargetPlayer))
            return;

        USkeletalMeshComponent *TargetMesh = TargetPlayer->GetMesh();
        if (!IsValidPtr(TargetMesh) || !TargetMesh->SkeletalMesh)
            return;

        bool isAimbotKeyPressed = false;

        // Verificar teclado/mouse
        bool isKeyboardMousePressed = PlayerController->IsInputKeyDown(Keys::CurrentAimbotKey);

        // Verificar gamepad
        SDK::FKey *aimbotGamepadButton = Keys::GetGamepadButtonByIndex(mods::gamepad_aimbot_button);
        bool isGamepadPressed = false;
        if (IsValidPtr(aimbotGamepadButton))
        {
            isGamepadPressed = PlayerController->IsInputKeyDown(*aimbotGamepadButton);
        }

        // Ativar se qualquer um dos inputs estiver pressionado
        isAimbotKeyPressed = isKeyboardMousePressed || isGamepadPressed;

        // Se as teclas funcionaram corretamente, resetar o contador de erros
        if (isKeyboardMousePressed || isGamepadPressed) {
            KeyCaptureSystem::ResetErrorCount();
        }

        if (!isAimbotKeyPressed)
        {
            // Resetar o rastreamento quando a tecla não está pressionada
            Variables::LastTarget = nullptr;
            Variables::TargetTrackingTime = 0.0f;
            Variables::IsAcquiringTarget = false;
            Variables::TargetAcquisitionTime = 0.0f;

            // Resetar os valores adaptativos para os iniciais
            Variables::CurrentSmoothAmount = mods::initialSmoothAmount;
            Variables::CurrentSmoothAmountPitch = mods::initialSmoothAmountPitch;
            Variables::CurrentSmoothAmountYaw = mods::initialSmoothAmountYaw;
            Variables::CurrentInertiaFactor = mods::initialInertiaFactor;

            // Resetar estado da tecla do Flick quando aimbot não está ativo
            mods::lastAimbotKeyState = false;

            return;
        }

        // Obter o deltaTime diretamente do motor do jogo (mais confiável)
        float deltaTime = UGameplayStatics::GetWorldDeltaSeconds(World);

        // Proteger apenas contra valores extremos perigosos (permite FPS altos)
        deltaTime = mods::Clamp(deltaTime, 0.0001f, 0.2f);

        // Verificar se o alvo mudou
        bool targetChanged = (Variables::LastTarget != TargetPlayer);

        // Se o alvo mudou, resetar o rastreamento
        if (targetChanged)
        {
            Variables::LastTarget = TargetPlayer;
            Variables::TargetTrackingTime = 0.0f;

            // Iniciar o processo de aquisição se o delay estiver ativado
            if (mods::useAimDelay)
            {
                Variables::IsAcquiringTarget = true;
                Variables::TargetAcquisitionTime = 0.0f;
            }

            // Resetar os valores adaptativos para os iniciais
            Variables::CurrentSmoothAmount = mods::initialSmoothAmount;
            Variables::CurrentSmoothAmountPitch = mods::initialSmoothAmountPitch;
            Variables::CurrentSmoothAmountYaw = mods::initialSmoothAmountYaw;
            Variables::CurrentInertiaFactor = mods::initialInertiaFactor;
        }
        else
        {
            // Incrementar o tempo de rastreamento
            Variables::TargetTrackingTime += deltaTime;

            // Se estamos em processo de aquisição, incrementar o tempo
            if (Variables::IsAcquiringTarget)
            {
                Variables::TargetAcquisitionTime += deltaTime;

                // Verificar se o tempo de aquisição foi atingido
                if (Variables::TargetAcquisitionTime >= mods::aimDelayTime)
                {
                    Variables::IsAcquiringTarget = false;
                }
                else
                {
                    // Ainda estamos adquirindo o alvo, não fazer nada
                    return;
                }
            }
        }

        // Atualizar o sistema de partes do corpo alvo
        float currentTime = SDK::UGameplayStatics::GetTimeSeconds(World);
        mods::UpdateTargetBodyPart(currentTime);

        // Definir os sockets para diferentes partes do corpo
        FName NeckSocketName = UKismetStringLibrary::Conv_StringToName(SDK::FString(L"neck_01"));
        FName ChestSocketName = UKismetStringLibrary::Conv_StringToName(SDK::FString(L"spine_02"));
        FName SpineSocketName = UKismetStringLibrary::Conv_StringToName(SDK::FString(L"spine_01"));

        // Verificar quais sockets existem
        bool hasNeckSocket = TargetMesh->DoesSocketExist(NeckSocketName);
        bool hasChestSocket = TargetMesh->DoesSocketExist(ChestSocketName);
        bool hasSpineSocket = TargetMesh->DoesSocketExist(SpineSocketName);

        // Escolher o socket alvo com base no modo selecionado
        FName TargetSocketName;

        switch (mods::targetBodyPartMode)
        {
        case 0: // Cabeça
            TargetSocketName = HeadSocketName;
            break;
        case 1: // Pescoço
            TargetSocketName = hasNeckSocket ? NeckSocketName : HeadSocketName;
            break;
        case 2: // Peito
            TargetSocketName = hasChestSocket ? ChestSocketName : (hasNeckSocket ? NeckSocketName : HeadSocketName);
            break;
        case 3: // Coluna
            TargetSocketName = hasSpineSocket ? SpineSocketName : (hasChestSocket ? ChestSocketName : HeadSocketName);
            break;
        case 4: // Aleatório
            switch (mods::currentRandomBodyPart)
            {
            case 0:
                TargetSocketName = HeadSocketName;
                break;
            case 1:
                TargetSocketName = hasNeckSocket ? NeckSocketName : HeadSocketName;
                break;
            case 2:
                TargetSocketName = hasChestSocket ? ChestSocketName : HeadSocketName;
                break;
            case 3:
                TargetSocketName = hasSpineSocket ? SpineSocketName : HeadSocketName;
                break;
            default:
                TargetSocketName = HeadSocketName;
                break;
            }
            break;
        case 5: // Humanizado
            switch (mods::humanizedBodyPart)
            {
            case 0:
                TargetSocketName = HeadSocketName;
                break;
            case 1:
                TargetSocketName = hasNeckSocket ? NeckSocketName : HeadSocketName;
                break;
            case 2:
                TargetSocketName = hasChestSocket ? ChestSocketName : HeadSocketName;
                break;
            case 3:
                TargetSocketName = hasSpineSocket ? SpineSocketName : HeadSocketName;
                break;
            default:
                TargetSocketName = HeadSocketName;
                break;
            }
            break;
        default:
            // Fallback para o comportamento antigo
            TargetSocketName = (hasNeckSocket && mods::useNeckTarget) ? NeckSocketName : HeadSocketName;
            break;
        }

        // Obter a posição 3D do alvo
        FVector Target3D = TargetMesh->GetSocketLocation(TargetSocketName);
        if (Target3D.IsZero())
            return;

        // Obter a velocidade do alvo
        SDK::FVector TargetVelocity = GetVelocity(TargetPlayer);

        // Obter a posição do jogador local
        SDK::FVector LocalPosition = PlayerCameraManager->GetCameraLocation();

        // Calcular a distância até o alvo
        float DistanceToTarget = (Target3D - LocalPosition).Magnitude();

        // Calcular a rotação para o alvo
        FRotator TargetRotation = UKismetMathLibrary::FindLookAtRotation(
            PlayerCameraManager->GetCameraLocation(),
            Target3D);

        // Projetar a posição 3D para 2D para calcular a distância ao centro
        FVector2D Target2D;
        PlayerController->ProjectWorldLocationToScreen(Target3D, &Target2D, true);
        double DistanceToCenter = UKismetMathLibrary::Vector_Distance2D(
            FVector(Variables::ScreenCenter.X, Variables::ScreenCenter.Y, 0),
            FVector(Target2D.X, Target2D.Y, 0));

        auto currentRotation = PlayerController->GetControlRotation();

        //---------------------------------------------------------------------
        // 		⚡	Sistema Flick Simplificado e Autônomo (Respeitando Aim Delay)
        //---------------------------------------------------------------------
        float flickSmoothingMultiplier = 1.0f;
        float flickInertiaMultiplier = 1.0f;

        // Detectar pressionamento da tecla (independente do aimbot)
        bool currentAimbotKeyState = isAimbotKeyPressed;
        bool keyJustPressed = currentAimbotKeyState && !mods::lastAimbotKeyState;
        mods::lastAimbotKeyState = currentAimbotKeyState;

        // Verificar se temos um alvo válido e se ele passa por todas as verificações de segurança
        bool hasValidTarget = IsValidObjectPtr(TargetPlayer);
        bool targetPassesChecks = true;

        if (hasValidTarget)
        {
            // Verificar team check se ativado
            if (mods::TeamCheck && IsValidObjectPtr(Variables::AcknowledgedPawn))
            {
                bool bIsAlly = SDK::UTeamFunctionLibrary::IsAlly(Variables::AcknowledgedPawn, TargetPlayer, true);
                if ((mods::teamTargetMode && !bIsAlly) || (!mods::teamTargetMode && bIsAlly))
                {
                    targetPassesChecks = false;
                }
            }

            // Verificar visibilidade se ativado
            if (targetPassesChecks && mods::VisCheck)
            {
                USkeletalMeshComponent *TargetMesh = TargetPlayer->GetMesh();
                if (IsValidObjectPtr(TargetMesh))
                {
                    FVector TargetHead3D = TargetMesh->GetSocketLocation(HeadSocketName);
                    bool isVisibleOld = TargetMesh->WasRecentlyRendered(SDK::ERecentlyRenderedType::OnScreen, 0.f);
                    bool isVisibleNew = IsPlayerVisible(PlayerController, TargetPlayer, TargetHead3D);
                    bool isVisible = mods::UseLineOfSight ? isVisibleNew : isVisibleOld;

                    if (!isVisible)
                    {
                        targetPassesChecks = false;
                    }
                }
                else
                {
                    targetPassesChecks = false;
                }
            }

            // Verificar FOV se não estivermos em modo Support Vision
            if (targetPassesChecks && !mods::isSupportVisionEnabled)
            {
                USkeletalMeshComponent *TargetMesh = TargetPlayer->GetMesh();
                if (IsValidObjectPtr(TargetMesh))
                {
                    FVector TargetHead3D = TargetMesh->GetSocketLocation(HeadSocketName);
                    FVector2D TargetHead2D;
                    if (PlayerController->ProjectWorldLocationToScreen(TargetHead3D, &TargetHead2D, true))
                    {
                        float actualFovCircle = (mods::fov * Variables::ScreenSize.X / mods::fov_changer_amount) / 2.0f;
                        if (!InCircle(actualFovCircle, TargetHead2D))
                        {
                            targetPassesChecks = false;
                        }
                    }
                    else
                    {
                        targetPassesChecks = false;
                    }
                }
                else
                {
                    targetPassesChecks = false;
                }
            }
        }

        // Ativar Flick ao pressionar a tecla (respeitando todos os limitadores)
        if (mods::useFlick && keyJustPressed && !Variables::IsAcquiringTarget && hasValidTarget && targetPassesChecks)
        {
            mods::flickIsActive = true;
            mods::flickStartTime = currentTime;
            printf("Flick ATIVADO - Duração: %.1fs (respeitando todos os limitadores)\n", mods::flickDuration);
        }

        // Processar Flick ativo (respeitando todos os limitadores)
        if (mods::useFlick && mods::flickIsActive && !Variables::IsAcquiringTarget && hasValidTarget && targetPassesChecks)
        {
            float elapsedTime = currentTime - mods::flickStartTime;

            // Verificar se o tempo de duração foi atingido
            if (elapsedTime >= mods::flickDuration)
            {
                mods::flickIsActive = false;
                printf("Flick DESATIVADO - Tempo completado (%.1fs)\n", elapsedTime);
            }
            else
            {
                // Calcular progresso da redução (0.0 a 1.0)
                float progress = elapsedTime / mods::flickDuration;
                progress = mods::Clamp(progress, 0.0f, 1.0f);

                // Aplicar curva de redução baseada no tipo selecionado
                float reductionCurve = 0.0f;
                const char* curveTypeName = "";

                switch (mods::flickReductionType)
                {
                    case 0: // Linear
                        reductionCurve = progress;
                        curveTypeName = "Linear";
                        break;

                    case 1: // Exponencial (rápido no início, suave no fim)
                        reductionCurve = 1.0f - std::exp(-3.0f * progress);
                        curveTypeName = "Exponencial";
                        break;

                    case 2: // Quadrático (suave no início, rápido no fim)
                        reductionCurve = progress * progress;
                        curveTypeName = "Quadrático";
                        break;

                    default:
                        reductionCurve = progress; // Fallback para linear
                        curveTypeName = "Linear (fallback)";
                        break;
                }

                // Aplicar redução: de 100% para 10% baseado na curva
                // Fórmula: 1.0 - (0.9 * curva) = vai de 1.0 (100%) para 0.1 (10%)
                flickSmoothingMultiplier = 1.0f - (0.9f * reductionCurve);
                flickInertiaMultiplier = 1.0f - (0.9f * reductionCurve);

                // Garantir que nunca vá abaixo de 10%
                flickSmoothingMultiplier = mods::Clamp(flickSmoothingMultiplier, 0.1f, 1.0f);
                flickInertiaMultiplier = mods::Clamp(flickInertiaMultiplier, 0.1f, 1.0f);

                // Log para depuração (limitado para evitar spam)
                static float lastFlickLogTime = 0.0f;
                if (currentTime - lastFlickLogTime > 1.0f) {
                    printf("Flick ATIVO - Tipo: %s, Progresso: %.2f, Curva: %.2f, Mult: %.2f, Tempo restante: %.1fs\n",
                           curveTypeName, progress, reductionCurve, flickSmoothingMultiplier, mods::flickDuration - elapsedTime);
                    lastFlickLogTime = currentTime;
                }
            }
        }

        // Se o Flick está ativo mas não pode ser processado, pausar mas manter o estado
        if (mods::useFlick && mods::flickIsActive &&
            (Variables::IsAcquiringTarget || !hasValidTarget || !targetPassesChecks))
        {
            // Log para indicar por que o Flick está pausado
            static float lastPauseLogTime = 0.0f;
            if (currentTime - lastPauseLogTime > 2.0f) {
                const char* pauseReason = "desconhecido";
                if (Variables::IsAcquiringTarget) {
                    pauseReason = "aim delay ativo";
                } else if (!hasValidTarget) {
                    pauseReason = "alvo inválido";
                } else if (!targetPassesChecks) {
                    pauseReason = "alvo não passou nas verificações de segurança";
                }

                printf("Flick PAUSADO - Motivo: %s\n", pauseReason);
                lastPauseLogTime = currentTime;
            }
        }

        // Sistema de suavização adaptativo
        if (mods::useAdaptiveMovement)
        {
            // Calcular o progresso da adaptação (0.0 a 1.0)
            float adaptiveProgress = mods::Clamp(Variables::TargetTrackingTime / mods::adaptiveDuration, 0.0f, 1.0f);

            // Aplicar curva quadrática para uma transição mais suave
            // Isso faz com que a adaptação comece mais lenta e acelere gradualmente
            float adaptiveCurve = adaptiveProgress * adaptiveProgress;

            if (mods::separateAdaptiveSettings)
            {
                // Interpolar entre os valores inicial e mínimo para pitch e yaw usando a curva
                Variables::CurrentSmoothAmountPitch = mods::initialSmoothAmountPitch -
                    (adaptiveCurve * (mods::initialSmoothAmountPitch - mods::minSmoothAmountPitch));

                Variables::CurrentSmoothAmountYaw = mods::initialSmoothAmountYaw -
                    (adaptiveCurve * (mods::initialSmoothAmountYaw - mods::minSmoothAmountYaw));
            }
            else
            {
                // Interpolar entre os valores inicial e mínimo usando a curva
                Variables::CurrentSmoothAmount = mods::initialSmoothAmount -
                    (adaptiveCurve * (mods::initialSmoothAmount - mods::minSmoothAmount));
            }

            // Atualizar o fator de inércia se a inércia adaptativa estiver ativada
            if (mods::useAdaptiveInertia)
            {
                Variables::CurrentInertiaFactor = mods::initialInertiaFactor -
                    (adaptiveCurve * (mods::initialInertiaFactor - mods::minInertiaFactor));
            }
        }

        // Ajustar o fator de suavização com base na distância
        float DistanceFactor = mods::Clamp(DistanceToTarget / 1000.0f, 0.5f, 2.0f);

        // Calcular a diferença normalizada entre a rotação atual e a rotação alvo
        FRotator DeltaRot = UKismetMathLibrary::NormalizedDeltaRotator(TargetRotation, currentRotation);

        // Aplicar variação aleatória para movimento mais natural
        float randomVariation = UKismetMathLibrary::RandomFloatInRange(0.95f, 1.05f);

        // Calcular a nova rotação com base nas configurações
        // Sistema de smoothing melhorado baseado no projeto de exemplo
        // Usa interpolação exponencial em vez de linear para movimento mais natural
        SDK::FRotator newRotation;

        if (mods::separatePitchYawSmoothing)
        {
            // Usar valores separados para Pitch e Yaw
            float smoothAmountPitch = mods::useAdaptiveMovement ? Variables::CurrentSmoothAmountPitch : mods::smoothAmountPitch;
            float smoothAmountYaw = mods::useAdaptiveMovement ? Variables::CurrentSmoothAmountYaw : mods::smoothAmountYaw;

            // Aplicar multiplicadores do sistema Flick
            smoothAmountPitch *= flickSmoothingMultiplier;
            smoothAmountYaw *= flickSmoothingMultiplier;

            // Normalizar para 0-1 para aplicar curva quadrática (igual ao projeto de exemplo)
            float normalizedPitch = smoothAmountPitch / 10.0f;
            float normalizedYaw = smoothAmountYaw / 10.0f;

            // Aplicar curva quadrática: y = x^2 (igual ao projeto de exemplo)
            float pitchCurve = normalizedPitch * normalizedPitch;
            float yawCurve = normalizedYaw * normalizedYaw;

            // Calcular o fator de suavização com escala exponencial (igual ao projeto de exemplo)
            // Valores menores de smoothAmount resultam em fatores maiores (menos suavização)
            // Valores maiores de smoothAmount resultam em fatores menores (mais suavização)
            float pitchFactor = mods::Clamp(std::exp(-4.0f * pitchCurve), 0.01f, 1.0f);
            float yawFactor = mods::Clamp(std::exp(-4.0f * yawCurve), 0.01f, 1.0f);

            // Calcular velocidade de interpolação exponencial (melhorado do projeto de exemplo)
            float InterpSpeedPitch = 20.0f * pitchFactor;
            float InterpSpeedYaw = 20.0f * yawFactor;

            // Ajustar com base na distância e adicionar variação aleatória
            InterpSpeedPitch *= DistanceFactor * randomVariation;
            InterpSpeedYaw *= DistanceFactor * randomVariation;

            if (mods::useInertia)
            {
                // Sistema de inércia melhorado (baseado no projeto de exemplo)
                float inertiaFactorToUse = mods::useAdaptiveInertia ? Variables::CurrentInertiaFactor : mods::inertiaFactor;

                // Aplicar multiplicador do sistema Flick
                inertiaFactorToUse *= flickInertiaMultiplier;

                // Normalizar para 0-1 (baseado no projeto de exemplo)
                float normalizedInertia = mods::Clamp(inertiaFactorToUse / 10.0f, 0.0f, 1.0f);

                // Aplicar curva quadrática: y = x^2 (igual ao projeto de exemplo)
                // Isso cria uma distribuição mais uniforme em todos os níveis
                float inertiaCurve = normalizedInertia * normalizedInertia;

                // Calcular o fator de inércia com base na curva (igual ao projeto de exemplo)
                // Valores maiores de inertiaFactor resultam em mais inércia
                float adjustedInertiaFactor = mods::Clamp(inertiaCurve, 0.01f, 0.99f);

                // Log para depuração (limitado para evitar spam) - baseado no projeto de exemplo
                static float lastInertiaLogTime = 0.0f;
                float currentInertiaTime = UGameplayStatics::GetTimeSeconds(UWorld::GetWorld());
                if (currentInertiaTime - lastInertiaLogTime > 5.0f) {
                    std::string adaptiveInfo = mods::useAdaptiveInertia ? " (adaptativo)" : "";
                    printf("Inércia aplicada%s - Valor original=%.3f, curva=%.3f, fator ajustado=%.3f\n",
                           adaptiveInfo.c_str(), normalizedInertia, inertiaCurve, adjustedInertiaFactor);
                    lastInertiaLogTime = currentInertiaTime;
                }

                // Calcular o delta de rotação suavizado (baseado no projeto de exemplo)
                SDK::FRotator rotationDelta;
                rotationDelta.Pitch = DeltaRot.Pitch * (1.0f - std::exp(-InterpSpeedPitch * deltaTime));
                rotationDelta.Yaw = DeltaRot.Yaw * (1.0f - std::exp(-InterpSpeedYaw * deltaTime));
                rotationDelta.Roll = 0.0f;

                // Interpolar entre a velocidade atual e a nova velocidade desejada (igual ao projeto de exemplo)
                // Quanto maior o adjustedInertiaFactor, mais a velocidade atual é preservada
                Variables::CurrentVelocity.Pitch = Variables::CurrentVelocity.Pitch * adjustedInertiaFactor +
                                                  rotationDelta.Pitch * (1.0f - adjustedInertiaFactor);
                Variables::CurrentVelocity.Yaw = Variables::CurrentVelocity.Yaw * adjustedInertiaFactor +
                                                rotationDelta.Yaw * (1.0f - adjustedInertiaFactor);

                // Aplicar a velocidade atual para obter a nova rotação (igual ao projeto de exemplo)
                newRotation.Pitch = currentRotation.Pitch + Variables::CurrentVelocity.Pitch;
                newRotation.Yaw = currentRotation.Yaw + Variables::CurrentVelocity.Yaw;
                newRotation.Roll = 0;

                // Adicionar informações de velocidade ao log de inércia (baseado no projeto de exemplo)
                if (currentInertiaTime - lastInertiaLogTime > 5.0f) {
                    printf("Velocidade aplicada - Pitch=%.3f, Yaw=%.3f\n",
                           Variables::CurrentVelocity.Pitch, Variables::CurrentVelocity.Yaw);
                }
            }
            else
            {
                // Interpolar suavemente sem inércia usando interpolação exponencial (do projeto de exemplo)
                // Fórmula: Current + Delta * (1.0 - std::exp(-InterpSpeed * DeltaTime))
                // Isso cria um efeito de desaceleração mais natural e suave
                float pitchInterpolationFactor = 1.0f - std::exp(-InterpSpeedPitch * deltaTime);
                float yawInterpolationFactor = 1.0f - std::exp(-InterpSpeedYaw * deltaTime);

                newRotation.Pitch = currentRotation.Pitch + DeltaRot.Pitch * pitchInterpolationFactor;
                newRotation.Yaw = currentRotation.Yaw + DeltaRot.Yaw * yawInterpolationFactor;
                newRotation.Roll = 0;
            }
        }
        else
        {
            // Usar valor único para ambos Pitch e Yaw
            float smoothAmount = mods::useAdaptiveMovement ? Variables::CurrentSmoothAmount : mods::smoothAmount;

            // Aplicar multiplicador do sistema Flick
            smoothAmount *= flickSmoothingMultiplier;

            // Normalizar para 0-1 para aplicar curva quadrática (igual ao projeto de exemplo)
            float normalizedSmooth = smoothAmount / 10.0f;

            // Aplicar curva quadrática: y = x^2 (igual ao projeto de exemplo)
            float smoothCurve = normalizedSmooth * normalizedSmooth;

            // Calcular o fator de suavização com escala exponencial (igual ao projeto de exemplo)
            float smoothFactor = mods::Clamp(std::exp(-4.0f * smoothCurve), 0.01f, 1.0f);

            // Calcular velocidade de interpolação exponencial (melhorado do projeto de exemplo)
            float InterpSpeed = 20.0f * smoothFactor;

            // Ajustar com base na distância e adicionar variação aleatória
            InterpSpeed *= DistanceFactor * randomVariation;

            if (mods::useInertia)
            {
                // Sistema de inércia melhorado (baseado no projeto de exemplo)
                float inertiaFactorToUse = mods::useAdaptiveInertia ? Variables::CurrentInertiaFactor : mods::inertiaFactor;

                // Aplicar multiplicador do sistema Flick
                inertiaFactorToUse *= flickInertiaMultiplier;

                // Normalizar para 0-1 (baseado no projeto de exemplo)
                float normalizedInertia = mods::Clamp(inertiaFactorToUse / 10.0f, 0.0f, 1.0f);

                // Aplicar curva quadrática: y = x^2 (igual ao projeto de exemplo)
                // Isso cria uma distribuição mais uniforme em todos os níveis
                float inertiaCurve = normalizedInertia * normalizedInertia;

                // Calcular o fator de inércia com base na curva (igual ao projeto de exemplo)
                // Valores maiores de inertiaFactor resultam em mais inércia
                float adjustedInertiaFactor = mods::Clamp(inertiaCurve, 0.01f, 0.99f);

                // Log para depuração (limitado para evitar spam) - baseado no projeto de exemplo
                static float lastInertiaLogTime2 = 0.0f;
                float currentInertiaTime2 = UGameplayStatics::GetTimeSeconds(UWorld::GetWorld());
                if (currentInertiaTime2 - lastInertiaLogTime2 > 5.0f) {
                    std::string adaptiveInfo = mods::useAdaptiveInertia ? " (adaptativo)" : "";
                    printf("Inércia aplicada%s - Valor original=%.3f, curva=%.3f, fator ajustado=%.3f\n",
                           adaptiveInfo.c_str(), normalizedInertia, inertiaCurve, adjustedInertiaFactor);
                    lastInertiaLogTime2 = currentInertiaTime2;
                }

                // Calcular o delta de rotação suavizado (baseado no projeto de exemplo)
                SDK::FRotator rotationDelta;
                rotationDelta.Pitch = DeltaRot.Pitch * (1.0f - std::exp(-InterpSpeed * deltaTime));
                rotationDelta.Yaw = DeltaRot.Yaw * (1.0f - std::exp(-InterpSpeed * deltaTime));
                rotationDelta.Roll = 0.0f;

                // Interpolar entre a velocidade atual e a nova velocidade desejada (igual ao projeto de exemplo)
                // Quanto maior o adjustedInertiaFactor, mais a velocidade atual é preservada
                Variables::CurrentVelocity.Pitch = Variables::CurrentVelocity.Pitch * adjustedInertiaFactor +
                                                  rotationDelta.Pitch * (1.0f - adjustedInertiaFactor);
                Variables::CurrentVelocity.Yaw = Variables::CurrentVelocity.Yaw * adjustedInertiaFactor +
                                                rotationDelta.Yaw * (1.0f - adjustedInertiaFactor);

                // Aplicar a velocidade atual para obter a nova rotação (igual ao projeto de exemplo)
                newRotation.Pitch = currentRotation.Pitch + Variables::CurrentVelocity.Pitch;
                newRotation.Yaw = currentRotation.Yaw + Variables::CurrentVelocity.Yaw;
                newRotation.Roll = 0;

                // Adicionar informações de velocidade ao log de inércia (baseado no projeto de exemplo)
                if (currentInertiaTime2 - lastInertiaLogTime2 > 5.0f) {
                    printf("Velocidade aplicada - Pitch=%.3f, Yaw=%.3f\n",
                           Variables::CurrentVelocity.Pitch, Variables::CurrentVelocity.Yaw);
                }
            }
            else
            {
                // Interpolar suavemente sem inércia usando interpolação exponencial (do projeto de exemplo)
                // Fórmula: Current + Delta * (1.0 - std::exp(-InterpSpeed * DeltaTime))
                // Isso cria um efeito de desaceleração mais natural e suave
                float interpolationFactor = 1.0f - std::exp(-InterpSpeed * deltaTime);

                newRotation.Pitch = currentRotation.Pitch + DeltaRot.Pitch * interpolationFactor;
                newRotation.Yaw = currentRotation.Yaw + DeltaRot.Yaw * interpolationFactor;
                newRotation.Roll = 0;
            }
        }

        // Armazenar a última rotação aplicada
        Variables::LastRotation = newRotation;

        // Aplicar a nova rotação
        PlayerController->SetControlRotation(newRotation);
    }

    //---------------------------------------------------------------------
    // 		🔍	Verificar se o projétil causou dano ao alvo
    //---------------------------------------------------------------------
    void CheckBulletTPDamage(AMarvelBaseCharacter *TargetPlayer, float currentTime)
    {
        // Verificar se o sistema de detecção de dano está ativado
        if (!mods::bulletTPShowDamageInfo)
            return;

        // Verificar se o alvo é válido
        if (!IsValidObjectPtr(TargetPlayer))
            return;

        // Verificar se é hora de verificar o dano
        if (currentTime - mods::bulletTPLastCheckTime < mods::bulletTPDamageCheckInterval)
            return;

        // Atualizar o tempo da última verificação
        mods::bulletTPLastCheckTime = currentTime;

        // Obter a saúde atual do alvo
        float currentHealth = TargetPlayer->GetCurrentHealth();

        // Se a saúde do alvo foi inicializada
        if (Variables::LastTargetHealth > 0.0f)
        {
            // Verificar se a saúde do alvo diminuiu
            if (currentHealth < Variables::LastTargetHealth)
            {
                // Calcular o dano causado
                float damageCaused = Variables::LastTargetHealth - currentHealth;

                // Atualizar contadores
                mods::bulletTPHitCount++;
                Variables::TotalProjectilesHit++;
                Variables::HasTargetHealthChanged = true;
                Variables::LastHealthChangeTime = currentTime;
                mods::bulletTPLastHitTime = currentTime;

                // Exibir informações de dano se o debug estiver ativado
                if (mods::bulletTPDebug)
                {
                    std::string message = "BulletTP: Dano causado: " + std::to_string(damageCaused) +
                                         " | Acertos: " + std::to_string(mods::bulletTPHitCount) +
                                         " | Erros: " + std::to_string(mods::bulletTPMissCount);
                    SafetySystem::LogError("BulletTP", message.c_str());
                }
            }
            // Verificar se projéteis foram teleportados mas não causaram dano
            else if (Variables::TrackedProjectiles.empty() && Variables::TotalProjectilesFired > 0 &&
                    !Variables::HasTargetHealthChanged &&
                    (currentTime - Variables::LastHealthChangeTime > 1.0f))
            {
                // Incrementar contador de erros
                mods::bulletTPMissCount++;

                // Exibir informações de erro se o debug estiver ativado
                if (mods::bulletTPDebug)
                {
                    std::string message = "BulletTP: Projétil não causou dano | Acertos: " +
                                         std::to_string(mods::bulletTPHitCount) +
                                         " | Erros: " + std::to_string(mods::bulletTPMissCount);
                    SafetySystem::LogError("BulletTP", message.c_str());
                }

                // Resetar o contador de projéteis disparados
                Variables::TotalProjectilesFired = 0;
            }
        }

        // Atualizar a última saúde do alvo
        Variables::LastTargetHealth = currentHealth;
    }

    //---------------------------------------------------------------------
    // 		🎯	Teleportar projéteis continuamente para o alvo em movimento (BulletTP)
    //---------------------------------------------------------------------
    void RunBulletTP(UWorld *World, APlayerController *PlayerController, APlayerCameraManager *PlayerCameraManager, AMarvelBaseCharacter *TargetPlayer, FName HeadSocketName, FName NeckSocketName, float actualBulletTPFovCircle)
    {
        // Verificações básicas
        if (!mods::bullet_tp || !TargetPlayer || !IsValidObjectPtr(TargetPlayer))
            return;

        USkeletalMeshComponent *TargetMesh = TargetPlayer->GetMesh();
        if (!IsValidObjectPtr(TargetMesh) || !TargetMesh->SkeletalMesh)
            return;

        // Verificar se o alvo está dentro do FOV do BulletTP
        FVector2D TargetHead2D;
        PlayerController->ProjectWorldLocationToScreen(TargetMesh->GetSocketLocation(HeadSocketName), &TargetHead2D, true);

        // Armazenar o centro da tela e o FOV para uso posterior
        SDK::FVector2D ScreenCenter = SDK::FVector2D(Variables::ScreenSize.X / 2, Variables::ScreenSize.Y / 2);
        bool isTargetInFOV = InCircle(actualBulletTPFovCircle, TargetHead2D);

        if (!isTargetInFOV)
            return;

        // Obter o tempo atual para controle de teleporte contínuo
        float currentTime = SDK::UGameplayStatics::GetTimeSeconds(World);

        // Verificar se o projétil causou dano ao alvo
        CheckBulletTPDamage(TargetPlayer, currentTime);

        // Verificar se a tecla do BulletTP está pressionada
        bool isBulletTPKeyPressed = false;

        // Verificar teclado/mouse
        bool isKeyboardMousePressed = PlayerController->IsInputKeyDown(Keys::CurrentBulletTPKey);

        // Verificar gamepad
        SDK::FKey *bulletTPGamepadButton = Keys::GetGamepadButtonByIndex(mods::gamepad_bullet_tp_button);
        bool isGamepadPressed = false;
        if (IsValidPtr(bulletTPGamepadButton))
        {
            isGamepadPressed = PlayerController->IsInputKeyDown(*bulletTPGamepadButton);
        }

        // Ativar se qualquer um dos inputs estiver pressionado
        isBulletTPKeyPressed = isKeyboardMousePressed || isGamepadPressed;

        // Se as teclas funcionaram corretamente, resetar o contador de erros
        if (isKeyboardMousePressed || isGamepadPressed) {
            KeyCaptureSystem::ResetErrorCount();
        }

        // Detectar quando a tecla é pressionada (transição de não pressionada para pressionada)
        bool keyJustPressed = isBulletTPKeyPressed && !mods::bulletTPWasKeyPressed;

        // Detectar quando a tecla é liberada (transição de pressionada para não pressionada)
        bool keyJustReleased = !isBulletTPKeyPressed && mods::bulletTPWasKeyPressed;

        // Se a tecla acabou de ser liberada, iniciar o período de teleporte após KeyUp
        if (keyJustReleased)
        {
            mods::bulletTPKeyUpTime = currentTime;
            mods::bulletTPContinueAfterKeyUp = true;

            if (mods::bulletTPDebug)
            {
                SafetySystem::LogError("BulletTP", "Tecla liberada, continuando teleporte por um período");
            }
        }

        // Verificar se ainda estamos no período de teleporte após KeyUp
        // Ajustar a escala para trabalhar com valores de 0.0 a 10.0
        // Converter para uma escala de 0.0 a 3.0 para manter a mesma sensação
        float adjustedKeyUpDuration = mods::bulletTPKeyUpDuration * 0.3f;
        bool isInKeyUpTeleportPeriod = mods::bulletTPContinueAfterKeyUp &&
                                      (currentTime - mods::bulletTPKeyUpTime <= adjustedKeyUpDuration);

        // Se o período de teleporte após KeyUp terminou, desativar o flag
        if (mods::bulletTPContinueAfterKeyUp && !isInKeyUpTeleportPeriod)
        {
            mods::bulletTPContinueAfterKeyUp = false;

            if (mods::bulletTPDebug)
            {
                SafetySystem::LogError("BulletTP", "Período de teleporte após soltar a tecla encerrado");
            }
        }

        // Atualizar o estado anterior da tecla para a próxima verificação
        mods::bulletTPWasKeyPressed = isBulletTPKeyPressed;

        // Verificar se devemos teleportar:
        // 1. A tecla está sendo pressionada (KeyDown)
        // 2. OU estamos no período de teleporte após a tecla ser liberada (KeyUp)
        bool shouldTeleport = isBulletTPKeyPressed || isInKeyUpTeleportPeriod;

        // Se não devemos teleportar, retornar
        if (!shouldTeleport)
            return;

        // Determinar qual socket usar (cabeça ou pescoço)
        FName SocketName = (mods::bullet_tp_target == 0) ? HeadSocketName :
                          (mods::bullet_tp_target == 1) ? NeckSocketName :
                          (rand() % 2 == 0) ? HeadSocketName : NeckSocketName;

        if (SocketName == NeckSocketName && !TargetMesh->DoesSocketExist(NeckSocketName))
        {
            SocketName = HeadSocketName;
        }

        // Obter a posição 3D atualizada do alvo
        FVector TargetHead3D = TargetMesh->GetSocketLocation(SocketName);
        if (TargetHead3D.IsZero() || !IsValidObjectPtr(Variables::AcknowledgedPawn))
            return;

        // Armazenar a posição do alvo para uso futuro
        Variables::LastTargetPosition = TargetHead3D;

        // Obter a posição da câmera (origem do tiro)
        SDK::FVector CameraLocation = PlayerCameraManager->GetCameraLocation();

        // Obter o deltaTime para calcular o movimento suave
        float DeltaTime = UGameplayStatics::GetWorldDeltaSeconds(World);

        // Proteger apenas contra valores extremos perigosos (permite FPS altos)
        DeltaTime = mods::Clamp(DeltaTime, 0.0001f, 0.2f);

        // Obter todos os projéteis no mundo
        SDK::TArray<SDK::AActor *> Bullets;
        SDK::UGameplayStatics::GetAllActorsOfClass(World, SDK::AGameplayAbilityTargetActor::StaticClass(), &Bullets);

        // Se a tecla acabou de ser pressionada ou se é a primeira vez que estamos teleportando
        if (keyJustPressed || Variables::TrackedProjectiles.empty())
        {
            // Limpar projéteis antigos que podem não existir mais
            Variables::TrackedProjectiles.clear();

            // Procurar por projéteis válidos para marcar para teleporte
            for (int i = 0; i < Bullets.Num(); i++)
            {
                if (!Bullets.IsValidIndex(i))
                    continue;

                auto Bullet = Bullets[i];
                if (!IsValidObjectPtr(Bullet) || Bullet->GetOwner() != Variables::AcknowledgedPawn)
                    continue;

                // Obter a posição atual do projétil
                SDK::FVector BulletLocation = Bullet->GetTransform().Translation;

                // Verificar se o projétil está dentro do FOV do BulletTP
                FVector2D Bullet2D;
                bool isBulletInScreen = PlayerController->ProjectWorldLocationToScreen(BulletLocation, &Bullet2D, true);

                // Se o projétil não estiver na tela, verificar se está dentro do FOV
                bool isBulletInFOV = isBulletInScreen && InCircle(actualBulletTPFovCircle, Bullet2D);

                // Calcular direções e distâncias
                SDK::FVector DirectionToTarget = (TargetHead3D - CameraLocation).GetNormalized();
                SDK::FVector DirectionToBullet = (BulletLocation - CameraLocation).GetNormalized();

                float BulletDistance = (BulletLocation - CameraLocation).Magnitude();
                float TargetDistance = (TargetHead3D - CameraLocation).Magnitude();

                // Verificar se o projétil está indo na direção do alvo e não passou dele
                float DotProduct = DirectionToTarget.Dot(DirectionToBullet);

                // Critério para capturar projéteis:
                // 1. O projétil deve estar dentro do FOV do BulletTP
                // 2. O projétil deve estar indo na direção do alvo
                // 3. O projétil não deve ter passado do alvo
                if (isBulletInFOV && DotProduct > 0.1f && BulletDistance < TargetDistance * 2.0f)
                {
                    // Marcar este projétil para teleporte contínuo
                    Variables::TrackedProjectiles.push_back(reinterpret_cast<uint64_t>(Bullet));

                    // Incrementar o contador de projéteis disparados
                    Variables::TotalProjectilesFired++;

                    // Resetar o flag de mudança de saúde
                    Variables::HasTargetHealthChanged = false;

                    // Teleportar o projétil imediatamente para uma posição intermediária
                    // Isso resolve o problema de atraso no início do teleporte
                    SDK::FVector DirectionExact = (TargetHead3D - BulletLocation).GetNormalized();
                    SDK::FVector InitialTeleportPosition = BulletLocation + (DirectionExact * 50.0f);
                    SDK::FRotator InitialRotation = UKismetMathLibrary::FindLookAtRotation(InitialTeleportPosition, TargetHead3D);

                    SDK::FHitResult initialHit;
                    Bullet->K2_SetActorLocationAndRotation(InitialTeleportPosition, InitialRotation, false, &initialHit, false);

                    if (mods::bulletTPDebug)
                    {
                        std::string message = "BulletTP: Projétil adicionado para teleporte | Total: " +
                                             std::to_string(Variables::TotalProjectilesFired);
                        SafetySystem::LogError("BulletTP", message.c_str());
                    }
                }
            }
        }
        else
        {
            // Verificar se há novos projéteis para adicionar à lista de rastreamento
            // Isso permite capturar projéteis disparados enquanto a tecla está pressionada
            for (int i = 0; i < Bullets.Num(); i++)
            {
                if (!Bullets.IsValidIndex(i))
                    continue;

                auto Bullet = Bullets[i];
                if (!IsValidObjectPtr(Bullet) || Bullet->GetOwner() != Variables::AcknowledgedPawn)
                    continue;

                // Verificar se este projétil já está sendo rastreado
                bool alreadyTracked = false;
                for (size_t j = 0; j < Variables::TrackedProjectiles.size(); j++)
                {
                    if (Variables::TrackedProjectiles[j] == reinterpret_cast<uint64_t>(Bullet))
                    {
                        alreadyTracked = true;
                        break;
                    }
                }

                // Se o projétil não estiver sendo rastreado, verificar se deve ser adicionado
                if (!alreadyTracked)
                {
                    // Obter a posição atual do projétil
                    SDK::FVector BulletLocation = Bullet->GetTransform().Translation;

                    // Verificar se o projétil está dentro do FOV do BulletTP
                    FVector2D Bullet2D;
                    bool isBulletInScreen = PlayerController->ProjectWorldLocationToScreen(BulletLocation, &Bullet2D, true);

                    // Se o projétil não estiver na tela, verificar se está dentro do FOV
                    bool isBulletInFOV = isBulletInScreen && InCircle(actualBulletTPFovCircle, Bullet2D);

                    // Calcular direções e distâncias
                    SDK::FVector DirectionToTarget = (TargetHead3D - CameraLocation).GetNormalized();
                    SDK::FVector DirectionToBullet = (BulletLocation - CameraLocation).GetNormalized();

                    float BulletDistance = (BulletLocation - CameraLocation).Magnitude();
                    float TargetDistance = (TargetHead3D - CameraLocation).Magnitude();

                    // Verificar se o projétil está indo na direção do alvo e não passou dele
                    float DotProduct = DirectionToTarget.Dot(DirectionToBullet);

                    // Critério para capturar projéteis:
                    // 1. O projétil deve estar dentro do FOV do BulletTP
                    // 2. O projétil deve estar indo na direção do alvo
                    // 3. O projétil não deve ter passado do alvo
                    if (isBulletInFOV && DotProduct > 0.1f && BulletDistance < TargetDistance * 2.0f)
                    {
                        // Marcar este projétil para teleporte contínuo
                        Variables::TrackedProjectiles.push_back(reinterpret_cast<uint64_t>(Bullet));

                        // Incrementar o contador de projéteis disparados
                        Variables::TotalProjectilesFired++;

                        // Resetar o flag de mudança de saúde
                        Variables::HasTargetHealthChanged = false;

                        // Teleportar o projétil imediatamente para uma posição intermediária
                        // Isso resolve o problema de atraso no início do teleporte
                        SDK::FVector DirectionExact = (TargetHead3D - BulletLocation).GetNormalized();
                        SDK::FVector InitialTeleportPosition = BulletLocation + (DirectionExact * 50.0f);
                        SDK::FRotator InitialRotation = UKismetMathLibrary::FindLookAtRotation(InitialTeleportPosition, TargetHead3D);

                        SDK::FHitResult initialHit;
                        Bullet->K2_SetActorLocationAndRotation(InitialTeleportPosition, InitialRotation, false, &initialHit, false);

                        if (mods::bulletTPDebug)
                        {
                            std::string message = "BulletTP: Novo projétil adicionado durante rastreamento | Total: " +
                                                 std::to_string(Variables::TotalProjectilesFired);
                            SafetySystem::LogError("BulletTP", message.c_str());
                        }
                    }
                }
            }
        }

        // Processar todos os projéteis marcados para teleporte
        for (size_t i = 0; i < Variables::TrackedProjectiles.size(); i++)
        {
            auto Bullet = reinterpret_cast<SDK::AActor*>(Variables::TrackedProjectiles[i]);

            // Verificar se o projétil ainda é válido
            if (!IsValidObjectPtr(Bullet) || Bullet->GetOwner() != Variables::AcknowledgedPawn)
            {
                // Remover projéteis inválidos da lista
                Variables::TrackedProjectiles.erase(Variables::TrackedProjectiles.begin() + i);
                i--; // Ajustar o índice após a remoção
                continue;
            }

            // Obter a posição atual do projétil
            SDK::FVector BulletLocation = Bullet->GetTransform().Translation;

            // Calcular a direção exata para o alvo a partir da posição atual do projétil
            SDK::FVector DirectionExact = (TargetHead3D - BulletLocation).GetNormalized();

            // Obter a velocidade do projétil
            float ProjectileSpeed = GetCurrentHeroProjectileSpeed("RunBulletTP");

            // Garantir que temos uma velocidade válida
            if (ProjectileSpeed < 100.0f)
                ProjectileSpeed = DEFAULT_PROJECTILE_SPEED;

            // Calcular a distância que o projétil deve percorrer neste frame com base em sua velocidade
            float DistanceToMove = ProjectileSpeed * DeltaTime;

            // Calcular a distância total até o alvo
            float TotalDistance = (TargetHead3D - BulletLocation).Magnitude();

            // Verificar se o projétil já passou do alvo
            bool passedTarget = false;

            // Calcular a direção do projétil para o alvo
            SDK::FVector DirectionToTargetFromBullet = (TargetHead3D - BulletLocation).GetNormalized();

            // Obter a velocidade atual do projétil (se disponível)
            SDK::FVector BulletVelocity;
            if (IsValidObjectPtr(Bullet->RootComponent))
            {
                BulletVelocity = Bullet->RootComponent->GetComponentVelocity();
            }

            // Se a velocidade for muito baixa, usar a direção baseada na rotação
            if (BulletVelocity.Magnitude() < 10.0f)
            {
                // Obter a rotação atual do projétil
                SDK::FRotator CurrentRotation = Bullet->K2_GetActorRotation();

                // Converter a rotação em um vetor de direção
                SDK::FVector ForwardVector;
                ForwardVector.X = SDK::UKismetMathLibrary::DegCos(CurrentRotation.Pitch) * SDK::UKismetMathLibrary::DegCos(CurrentRotation.Yaw);
                ForwardVector.Y = SDK::UKismetMathLibrary::DegCos(CurrentRotation.Pitch) * SDK::UKismetMathLibrary::DegSin(CurrentRotation.Yaw);
                ForwardVector.Z = SDK::UKismetMathLibrary::DegSin(CurrentRotation.Pitch);

                BulletVelocity = ForwardVector.GetNormalized() * ProjectileSpeed;
            }

            // Normalizar a velocidade do projétil
            SDK::FVector BulletDirection = BulletVelocity.GetNormalized();

            // Calcular o produto escalar entre a direção do projétil e a direção para o alvo
            float DirectionDotProduct = BulletDirection.Dot(DirectionToTargetFromBullet);

            // Se o produto escalar for negativo, o projétil já passou do alvo
            passedTarget = (DirectionDotProduct < 0.0f);

            // Se estamos muito próximos do alvo ou já passamos dele, teleportar diretamente para próximo dele
            if (TotalDistance <= DistanceToMove + 10.0f || passedTarget)
            {
                // Teleportar para 5 unidades antes do alvo para garantir que o projétil o atinja
                SDK::FVector FinalPosition = TargetHead3D - (DirectionExact * 5.0f);

                // Calcular a rotação correta para o projétil (olhando para o alvo)
                SDK::FRotator BulletRotation = UKismetMathLibrary::FindLookAtRotation(FinalPosition, TargetHead3D);

                // Armazenar a rotação para uso futuro
                Variables::LastProjectileRotation = BulletRotation;

                // Teleportar o projétil para a posição final com a rotação correta
                SDK::FHitResult fhit;
                Bullet->K2_SetActorLocationAndRotation(FinalPosition, BulletRotation, false, &fhit, false);

                // Não tentamos definir a velocidade diretamente, pois isso pode causar problemas de compilação
                // Em vez disso, confiamos na rotação correta do projétil para manter a trajetória

                // Se o projétil colidiu com algo, verificar se foi com o alvo
                if (fhit.bBlockingHit && IsValidObjectPtr(fhit.HitObjectHandle.Actor.Get()))
                {
                    // Se colidiu com o alvo, remover o projétil da lista e registrar o acerto
                    if (fhit.HitObjectHandle.Actor.Get() == TargetPlayer)
                    {
                        // Registrar o acerto direto (sem esperar pela verificação de saúde)
                        if (mods::bulletTPDebug)
                        {
                            SafetySystem::LogError("BulletTP", "Colisão direta com o alvo detectada!");
                        }

                        // Remover o projétil da lista
                        Variables::TrackedProjectiles.erase(Variables::TrackedProjectiles.begin() + i);
                        i--; // Ajustar o índice após a remoção
                        continue;
                    }
                }
            }
            else
            {
                // Calcular a posição ideal do projétil (mais próxima do alvo para evitar órbitas)
                // Ajustar a escala para trabalhar com valores de 0.0 a 10.0
                // Converter para uma escala de 0.9 a 1.0 para manter a mesma sensação
                float adjustedHitPrecision = 0.9f + (mods::bulletTPHitPrecision / 100.0f);
                float teleportDistance = TotalDistance * adjustedHitPrecision;

                // Calcular a nova posição do projétil - usar uma distância maior para garantir que o projétil chegue ao alvo
                SDK::FVector NewPosition = BulletLocation + (DirectionExact * (DistanceToMove * 1.5f));

                // Calcular a rotação correta para o projétil (olhando para o alvo)
                SDK::FRotator BulletRotation = UKismetMathLibrary::FindLookAtRotation(NewPosition, TargetHead3D);

                // Armazenar a rotação para uso futuro
                Variables::LastProjectileRotation = BulletRotation;

                // Teleportar o projétil para a nova posição com a rotação correta
                SDK::FHitResult fhit;
                Bullet->K2_SetActorLocationAndRotation(NewPosition, BulletRotation, false, &fhit, false);

                // Não tentamos definir a velocidade diretamente, pois isso pode causar problemas de compilação
                // Em vez disso, confiamos na rotação correta do projétil para manter a trajetória

                // Verificar se o projétil colidiu com algo
                if (fhit.bBlockingHit && IsValidObjectPtr(fhit.HitObjectHandle.Actor.Get()))
                {
                    // Se colidiu com o alvo, remover o projétil da lista e registrar o acerto
                    if (fhit.HitObjectHandle.Actor.Get() == TargetPlayer)
                    {
                        // Registrar o acerto direto (sem esperar pela verificação de saúde)
                        if (mods::bulletTPDebug)
                        {
                            SafetySystem::LogError("BulletTP", "Colisão direta com o alvo detectada!");
                        }

                        // Remover o projétil da lista
                        Variables::TrackedProjectiles.erase(Variables::TrackedProjectiles.begin() + i);
                        i--; // Ajustar o índice após a remoção
                    }
                }
            }
        }

        // Atualizar o tempo do último teleporte
        Variables::LastBulletTPTime = currentTime;

        // Limpar a lista de projéteis
        Bullets.Clear();

        // Exibir estatísticas de dano na tela se ativado
        if (mods::bulletTPShowDamageInfo && (mods::bulletTPHitCount > 0 || mods::bulletTPMissCount > 0))
        {
            // Calcular a taxa de acerto
            int totalShots = mods::bulletTPHitCount + mods::bulletTPMissCount;
            float hitRate = (totalShots > 0) ? (float)mods::bulletTPHitCount / totalShots * 100.0f : 0.0f;

            // Formatar a mensagem
            std::string message = "BulletTP Stats: ";
            message += "Hits: " + std::to_string(mods::bulletTPHitCount) + " | ";
            message += "Misses: " + std::to_string(mods::bulletTPMissCount) + " | ";
            message += "Hit Rate: " + std::to_string((int)hitRate) + "%";

            // Exibir a mensagem no canto superior esquerdo da tela
            // Esta mensagem será exibida pelo sistema de renderização
            if (mods::bulletTPDebug)
            {
                SafetySystem::LogError("BulletTP", message.c_str());
            }
        }
    }

    // Função para aplicar o efeito de Glow (contorno) nos jogadores inimigos
    void GlowRoutine(APlayerController *PlayerController, TArray<AActor *> Players)
    {
        if (!mods::enableGlow || !PlayerController)
            return;

        // Obter o personagem local
        auto LocalBaseChar = static_cast<AMarvelBaseCharacter *>(PlayerController->AcknowledgedPawn);
        if (!LocalBaseChar || !IsValidPtr(LocalBaseChar))
            return;

        // Ativar o contorno dos inimigos para o jogador local
        LocalBaseChar->ClientShowEnemyOutLine(true);

        // Definir o status do contorno (sempre através de paredes)
        auto OutlineStatus = SDK::ETeamOutlineShowStatus::ETOSS_Always;

        // Iterar por todos os jogadores
        for (int i = 0; i < static_cast<int>(Players.Num()); i++)
        {
            if (!Players.IsValidIndex(i))
                continue;

            AActor *Player = Players[i];
            if (!Player || Player == PlayerController->AcknowledgedPawn)
                continue;

            // Verificar se é um personagem válido
            auto BaseChar = static_cast<AMarvelBaseCharacter *>(Player);
            if (!BaseChar || !IsValidPtr(BaseChar))
                continue;

            // Verificar se os personagens são válidos
            if (!IsValidObjectPtr(Variables::AcknowledgedPawn) || !IsValidObjectPtr(BaseChar))
                continue;

            // Usar o SDK::UTeamFunctionLibrary::IsAlly para verificar se é aliado
            // Sem usar __try/__except para evitar problemas com liberação de objetos
            bool bIsAlly = SDK::UTeamFunctionLibrary::IsAlly(Variables::AcknowledgedPawn, BaseChar, true);

            if (bIsAlly)
            {
                continue; // Skip same-team players
            }

            // Aplicar o contorno ao mesh do jogador
            USkeletalMeshComponent *Mesh = BaseChar->GetMesh();
            if (Mesh && IsValidPtr(Mesh))
            {
                Mesh->SetTeamOutlineShowStatus(OutlineStatus);
            }
        }
    }

} // namespace CheatFeatures

#endif // CHEAT_FEATURES_H